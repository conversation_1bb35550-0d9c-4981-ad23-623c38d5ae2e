{"deposit": {"minAmount": "5", "options": ["100", "300", "500", "1000"]}, "defaultNetwork": "Ethereum", "networks": {"Ethereum": {"assetId": "USDC", "assetType": "Token", "address": "******************************************"}, "Base": {"contexts": ["<PERSON><PERSON><PERSON>", "Custodial"], "assetId": "USDC_BASECHAIN_ETH_5I5C", "assetType": "Token", "address": "******************************************"}, "Arbitrum": {"contexts": ["<PERSON><PERSON><PERSON>", "Custodial"], "assetId": "USDC_ARB_3SBJ", "assetType": "Token", "address": "******************************************"}, "Polygon": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetType": "Token", "address": "******************************************"}, "Optimism": {"contexts": ["<PERSON><PERSON><PERSON>", "Custodial"], "assetId": "USDC_OPT_9T08", "assetType": "Token", "address": "******************************************"}, "Solana": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetType": "Token", "address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}, "CELO": {"contexts": ["Custodial"], "assetId": "USDC_CELO_ROI8", "assetType": "Token", "address": "******************************************", "public": false}}}