{"deposit": {"minAmount": "5", "options": ["100", "300", "500", "1000"]}, "defaultNetwork": "Ethereum", "networks": {"Ethereum": {"assetId": "USDC", "assetType": "Token", "address": "******************************************"}, "Base": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetType": "Token", "address": "******************************************"}, "Arbitrum": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetType": "Token", "address": "******************************************"}, "Polygon": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetType": "Token", "address": "******************************************"}, "Optimism": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetType": "Token", "address": "******************************************"}, "Solana": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetType": "Token", "address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}}}