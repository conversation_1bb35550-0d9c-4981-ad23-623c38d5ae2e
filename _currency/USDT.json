{"deposit": {"minAmount": "5", "options": ["100", "300", "500", "1000"]}, "defaultNetwork": "Ethereum", "networks": {"Ethereum": {"assetId": "USDT_ERC20", "assetType": "Token", "address": "******************************************"}, "Arbitrum": {"contexts": ["<PERSON><PERSON><PERSON>", "Custodial"], "assetId": "USDT_ARB", "assetType": "Token", "address": "******************************************"}, "Polygon": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetType": "Token", "address": "******************************************"}, "Optimism": {"contexts": ["<PERSON><PERSON><PERSON>", "Custodial"], "assetId": "USDT", "assetType": "Token", "address": "******************************************"}, "Tron": {"contexts": ["Custodial"], "assetId": "TRX_USDT_S2UZ", "assetType": "Token"}, "Solana": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetType": "Token", "address": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"}, "CELO": {"contexts": ["Custodial"], "assetId": "USDT_CELO", "assetType": "Token", "address": "******************************************", "public": false}}}