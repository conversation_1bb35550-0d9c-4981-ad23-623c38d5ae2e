{"deposit": {"minAmount": "0.002", "options": ["0.1", "0.2", "0.5", "1"]}, "defaultNetwork": "Ethereum", "networks": {"Ethereum": {"assetId": "ETH", "assetType": "Native"}, "Base": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetId": "BASECHAIN_ETH", "assetType": "Native"}, "Arbitrum": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetId": "ETH-AETH", "assetType": "Native"}, "Optimism": {"contexts": ["<PERSON><PERSON><PERSON>"], "assetId": "ETH-OPT", "assetType": "Native"}, "ZKSync": {"contexts": ["Custodial"], "assetId": "ETH_ZKSYNC_ERA", "assetType": "Native", "public": false}, "Linea": {"contexts": ["Custodial"], "assetId": "LINEA", "assetType": "Native", "public": false}}}