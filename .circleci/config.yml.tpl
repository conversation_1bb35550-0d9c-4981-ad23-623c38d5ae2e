######################################################################################################################################################################################
# !! NOTICE !!                                                                                                                                                                       #
# This configuration file has been generated with a template, if you need to make changes, edit the config.yml.tpl file and run the 'yarn circle-gen' in the root of the repo.      #
######################################################################################################################################################################################

version: 2.1

parameters:
  destroy_developer_api:
    type: string
    default: ""

jobs:
  build-all:
    docker:
      - image: cimg/node:20.14
    resource_class: xlarge
    parameters:
      persist:
        type: boolean
        default: false
    steps:
      - checkout
      - restore_cache:
          name: Restore Yarn Package Cache
          keys:
            - yarn-packages-v1-{{ .Branch }}-{{ checksum "yarn.lock" }}
            - yarn-packages-v1-{{ .Branch }}-
            - yarn-packages-v1-
      - run:
          name: Authenticate with registry
          command: echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > ~/.npmrc
      - run:
          name: Install
          command: yarn ci
      - save_cache:
          name: Save Yarn Package Cache
          key: yarn-packages-v1-{{ .Branch }}-{{ checksum "yarn.lock" }}
          paths:
            - ~/.cache/yarn
      - run:
          name: Lint
          command: yarn lint
      - run:
          name: Build
          command: yarn build
          environment:
            DISABLE_V8_COMPILE_CACHE: 1
      - run:
          name: Test
          command: yarn test
          when: always
          environment:
            DISABLE_V8_COMPILE_CACHE: 1
      - run:
          name: Copy Results
          command: yarn test-helper copy ~/test-results
          when: always
      - store_test_results:
          path: ~/test-results
      - store_artifacts:
          path: ~/test-results
      - when:
          condition: << parameters.persist >>
          steps:
            - persist_to_workspace:
                root: .
                paths:
                  - .
  deploy:
    docker:
      - image: cimg/node:20.14
    resource_class: << parameters.resource_class >>
    parameters:
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      location:
        type: string
      resource_class:
        type: string
        default: "medium"
    environment:
      NODE_OPTIONS: --max_old_space_size=4096
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run:
          name: Deploy to << parameters.stage >> in region << parameters.region >>
          command: yarn --cwd "./<< parameters.location >>" deploy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >>
  deploy-docker:
    docker:
      - image: cimg/node:20.14
    parameters:
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      location:
        type: string
    steps:
      - attach_workspace:
          at: .
      - setup_remote_docker:
          docker_layer_caching: true
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run:
          name: Deploy to << parameters.stage >> in region << parameters.region >>
          command: yarn --cwd "./<< parameters.location >>" deploy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >>
  deploy-docs:
    docker:
      - image: cimg/node:20.14
    resource_class: << parameters.resource_class >>
    parameters:
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      api:
        type: string
      resource_class:
        type: string
        default: "medium"
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run:
          name: Deploy API Docs - << parameters.api >>
          command: cd services/docs && yarn deploy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >> --api << parameters.api >>
  deploy-dev-api:
    docker:
      - image: cimg/node:20.14
    resource_class: << parameters.resource_class >>
    parameters:
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      location:
        type: string
      resource_class:
        type: string
        default: "medium"
    environment:
      NODE_OPTIONS: --max_old_space_size=4096
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run:
          name: Deploy to << parameters.stage >> in region << parameters.region >>
          command: |
            FRED="\033[31m"
            FGRN="\033[32m"

            if [[ $CIRCLE_BRANCH =~ ([A-Z]{2,}-[0-9]{1,}) ]] ; then
                TICKETID=${BASH_REMATCH[1]//-}
                TICKETID_LOWER=$(echo $TICKETID | tr "[:upper:]" "[:lower:]")
                echo -e "$FGRN Successfully got TicketID from branch name"
                echo -e "$FGRN TicketID: $TICKETID_LOWER"
                yarn --cwd "./<< parameters.location >>" deploy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >> --branchId $TICKETID_LOWER

            else
                echo -e "$FRED SKIPPING DEVELOPER API PROVISIONING"
                echo -e "$FRED Branch name does not contain valid Ticket ID: $CIRCLE_BRANCH"
                echo -e "$FRED To provision a dev api ensure you have a ticket ID in the branch name, eg. MET-1234-foo-bar or feature/MET-5678-bar-baz"
            fi
  destroy-dev-api:
    docker:
      - image: cimg/node:20.14
    parameters:
      destroy_api:
        type: string
        default: ""
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      location:
        type: string
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run: 
          name: Destroy Branch api
          command: |
            echo "Destroying API for << parameters.destroy_api >>"
            yarn --cwd "./<< parameters.location >>" destroy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >> --branchId << parameters.destroy_api >>

workflows:
  build:
    when: 
      not: << pipeline.parameters.destroy_developer_api >>
    jobs:
      - build-all:
          persist: true
          filters:
            branches:
              ignore:
<%# this %>
                <%# branches %>
                - <% this %>
                <%/ branches %>
<%/ this %>
      - deploy-dev-api:
          filters:
            branches:
              only:
                - /.*[A-Z]{2,}-[0-9]{1,}.*/
          requires:
            - build-all
          name: deploy-branch-api
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/combined"
      - deploy-dev-api:
          filters:
            branches:
              only:
                - /.*[A-Z]{2,}-[0-9]{1,}.*/
          requires:
            - build-all
          name: deploy-branch-mgnt
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/combined"
  destroy-developer-branch-api:
      when: << pipeline.parameters.destroy_developer_api >>
      jobs:
      - build-all:
          persist: true
          filters:
            branches:
              only:
                - dev
      - destroy-dev-api:
          requires:
            - build-all
          destroy_api: << pipeline.parameters.destroy_developer_api >>
          name: destroy-branch-api-<< pipeline.parameters.destroy_developer_api >>
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/combined"
      - destroy-dev-api:
          requires:
            - build-all
          destroy_api: << pipeline.parameters.destroy_developer_api >>
          name: destroy-branch-mgnt-<< pipeline.parameters.destroy_developer_api >>
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/combined"
  build-deploy:
    when: 
      not: << pipeline.parameters.destroy_developer_api >>
    jobs:
      - build-all:
          name: build-deploy
          persist: true
          filters:
            branches:
              only:
<%# this %>
                <%# branches %>
                - <% this %>
                <%/ branches %>
<%/ this %>
<%# this %>

      # Brand: <% name %>
  <%# regions %>
      # DEPLOY dev - <% this %>
      <%# ../groups %>
      # <% name %>
      <%# deployables %>
      <%# ifIn ../../this regions %>
      <%# if useDocker %>
      - deploy-docker:
      <% else %>
      - deploy:
      <%/ if %>
          name: <% ../../../brand %>-deploy-dev-<% name %>-<% ../../this %>
          requires:
            <%# if first %>
            - <% ../../../brand %>-deploy-dev-<% first.name %>-<% ../../this %>
            <% else %>
            - build-deploy
            <%/ if %>
          filters:
            branches:
              only: <% ../../../deployment/dev/branch %>
          context: platform-<% ../../../brand %>-dev
          region: <% ../../this %>
          brand: <% ../../../brand %>
          stage: dev
          location: "<% location %>"
          <%# if resourceClass %>
          resource_class: <% resourceClass %>
          <%/ if %>
      <%/ ifIn %>
      <%/ deployables %>
      <%/ ../groups %>
      <%# iff this '==' ../primaryRegion %>
      # Docs
      - deploy-docs:
          name: <% ../brand %>-deploy-mgnt-docs-dev-<% this %>
          requires:
              - build-deploy
          filters:
            branches:
              only: <% ../deployment/dev/branch %>
          context: platform-<% ../brand %>-dev
          region: <% this %>
          brand: <% ../brand %>
          stage: dev
          api: management
          resource_class: xlarge
      - deploy-docs:
          name: <% ../brand %>-deploy-client-docs-dev-<% this %>
          requires:
              - build-deploy
          filters:
            branches:
              only: <% ../deployment/dev/branch %>
          context: platform-<% ../brand %>-dev
          region: <% this %>
          brand: <% ../brand %>
          stage: dev
          api: client
          resource_class: xlarge
      <%/ iff %>
  <%/ regions %>
  <%# regions %>
      # DEPLOY prod - <% this %>
      <%# ../groups %>
      # <% name %>
      <%# deployables %>
      <%# ifIn ../../this regions %>
      <%# if useDocker %>
      - deploy-docker:
      <% else %>
      - deploy:
      <%/ if %>
          name: <% ../../../brand %>-deploy-prod-<% name %>-<% ../../this %>
          requires:
            <%# if first %>
            - <% ../../../brand %>-deploy-prod-<% first.name %>-<% ../../this %>
            <% else %>
            - build-deploy
            <%/ if %>
          filters:
            branches:
              only: <% ../../../deployment/prod/branch %>
          context: platform-<% ../../../brand %>-prod
          region: <% ../../this %>
          brand: <% ../../../brand %>
          stage: prod
          location: "<% location %>"
          <%# if resourceClass %>
          resource_class: <% resourceClass %>
          <%/ if %>
      <%/ ifIn %>
      <%/ deployables %>
      <%/ ../groups %>
  <%/ regions %>
<%/ this %>