######################################################################################################################################################################################
# !! NOTICE !!                                                                                                                                                                       #
# This configuration file has been generated with a template, if you need to make changes, edit the config.yml.tpl file and run the 'yarn circle-gen' in the root of the repo.      #
######################################################################################################################################################################################

version: 2.1

parameters:
  destroy_developer_api:
    type: string
    default: ""

jobs:
  build-all:
    docker:
      - image: cimg/node:20.14
    resource_class: xlarge
    parameters:
      persist:
        type: boolean
        default: false
    steps:
      - checkout
      - restore_cache:
          name: Restore Yarn Package Cache
          keys:
            - yarn-packages-v1-{{ .Branch }}-{{ checksum "yarn.lock" }}
            - yarn-packages-v1-{{ .Branch }}-
            - yarn-packages-v1-
      - run:
          name: Authenticate with registry
          command: echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" > ~/.npmrc
      - run:
          name: Install
          command: yarn ci
      - save_cache:
          name: Save Yarn Package Cache
          key: yarn-packages-v1-{{ .Branch }}-{{ checksum "yarn.lock" }}
          paths:
            - ~/.cache/yarn
      - run:
          name: Lint
          command: yarn lint
      - run:
          name: Build
          command: yarn build
          environment:
            DISABLE_V8_COMPILE_CACHE: 1
      - run:
          name: Test
          command: yarn test
          when: always
          environment:
            DISABLE_V8_COMPILE_CACHE: 1
      - run:
          name: Copy Results
          command: yarn test-helper copy ~/test-results
          when: always
      - store_test_results:
          path: ~/test-results
      - store_artifacts:
          path: ~/test-results
      - when:
          condition: << parameters.persist >>
          steps:
            - persist_to_workspace:
                root: .
                paths:
                  - .
  deploy:
    docker:
      - image: cimg/node:20.14
    resource_class: << parameters.resource_class >>
    parameters:
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      location:
        type: string
      resource_class:
        type: string
        default: "medium"
    environment:
      NODE_OPTIONS: --max_old_space_size=4096
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run:
          name: Deploy to << parameters.stage >> in region << parameters.region >>
          command: yarn --cwd "./<< parameters.location >>" deploy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >>
  deploy-docker:
    docker:
      - image: cimg/node:20.14
    parameters:
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      location:
        type: string
    steps:
      - attach_workspace:
          at: .
      - setup_remote_docker:
          docker_layer_caching: true
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run:
          name: Deploy to << parameters.stage >> in region << parameters.region >>
          command: yarn --cwd "./<< parameters.location >>" deploy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >>
  deploy-docs:
    docker:
      - image: cimg/node:20.14
    resource_class: << parameters.resource_class >>
    parameters:
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      api:
        type: string
      resource_class:
        type: string
        default: "medium"
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run:
          name: Deploy API Docs - << parameters.api >>
          command: cd services/docs && yarn deploy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >> --api << parameters.api >>
  deploy-dev-api:
    docker:
      - image: cimg/node:20.14
    resource_class: << parameters.resource_class >>
    parameters:
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      location:
        type: string
      resource_class:
        type: string
        default: "medium"
    environment:
      NODE_OPTIONS: --max_old_space_size=4096
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run:
          name: Deploy to << parameters.stage >> in region << parameters.region >>
          command: |
            FRED="\033[31m"
            FGRN="\033[32m"

            if [[ $CIRCLE_BRANCH =~ ([A-Z]{2,}-[0-9]{1,}) ]] ; then
                TICKETID=${BASH_REMATCH[1]//-}
                TICKETID_LOWER=$(echo $TICKETID | tr "[:upper:]" "[:lower:]")
                echo -e "$FGRN Successfully got TicketID from branch name"
                echo -e "$FGRN TicketID: $TICKETID_LOWER"
                yarn --cwd "./<< parameters.location >>" deploy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >> --branchId $TICKETID_LOWER

            else
                echo -e "$FRED SKIPPING DEVELOPER API PROVISIONING"
                echo -e "$FRED Branch name does not contain valid Ticket ID: $CIRCLE_BRANCH"
                echo -e "$FRED To provision a dev api ensure you have a ticket ID in the branch name, eg. MET-1234-foo-bar or feature/MET-5678-bar-baz"
            fi
  destroy-dev-api:
    docker:
      - image: cimg/node:20.14
    parameters:
      destroy_api:
        type: string
        default: ""
      brand:
        type: string
      stage:
        type: string
      region:
        type: string
      location:
        type: string
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Add AWS credentials
          command: mkdir ~/.aws && echo -e "[platform-<< parameters.brand >>-<< parameters.stage >>]\naws_access_key_id=$AWS_ACCESS_KEY_ID\naws_secret_access_key=$AWS_SECRET_ACCESS_KEY\n" > ~/.aws/credentials
      - run: 
          name: Destroy Branch api
          command: |
            echo "Destroying API for << parameters.destroy_api >>"
            yarn --cwd "./<< parameters.location >>" destroy --brand << parameters.brand >> --stage << parameters.stage >> --region << parameters.region >> --branchId << parameters.destroy_api >>

workflows:
  build:
    when: 
      not: << pipeline.parameters.destroy_developer_api >>
    jobs:
      - build-all:
          persist: true
          filters:
            branches:
              ignore:
                - dev
                - master
                - release/metawin-us/dev
                - release/metawin-us/prod
      - deploy-dev-api:
          filters:
            branches:
              only:
                - /.*[A-Z]{2,}-[0-9]{1,}.*/
          requires:
            - build-all
          name: deploy-branch-api
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/combined"
      - deploy-dev-api:
          filters:
            branches:
              only:
                - /.*[A-Z]{2,}-[0-9]{1,}.*/
          requires:
            - build-all
          name: deploy-branch-mgnt
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/combined"
  destroy-developer-branch-api:
      when: << pipeline.parameters.destroy_developer_api >>
      jobs:
      - build-all:
          persist: true
          filters:
            branches:
              only:
                - dev
      - destroy-dev-api:
          requires:
            - build-all
          destroy_api: << pipeline.parameters.destroy_developer_api >>
          name: destroy-branch-api-<< pipeline.parameters.destroy_developer_api >>
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/combined"
      - destroy-dev-api:
          requires:
            - build-all
          destroy_api: << pipeline.parameters.destroy_developer_api >>
          name: destroy-branch-mgnt-<< pipeline.parameters.destroy_developer_api >>
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/combined"
  build-deploy:
    when: 
      not: << pipeline.parameters.destroy_developer_api >>
    jobs:
      - build-all:
          name: build-deploy
          persist: true
          filters:
            branches:
              only:
                - dev
                - master
                - release/metawin-us/dev
                - release/metawin-us/prod

      # Brand: MetaWin
      # DEPLOY dev - eu-west-1
      # feat-tournaments
      - deploy-docker:
          name: metawin-deploy-dev-tournament-engine-bingo-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "containers/tournament/engine/bingo"
      - deploy-docker:
          name: metawin-deploy-dev-tournament-engine-crash-eu-west-1
          requires:
            - metawin-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "containers/tournament/engine/crash"
      - deploy-docker:
          name: metawin-deploy-dev-tournament-engine-hilo-eu-west-1
          requires:
            - metawin-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "containers/tournament/engine/hilo"
      - deploy-docker:
          name: metawin-deploy-dev-tournament-engine-slot-eu-west-1
          requires:
            - metawin-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "containers/tournament/engine/slot"
      - deploy:
          name: metawin-deploy-dev-service-client-tournament-eu-west-1
          requires:
            - metawin-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/tournament"
      - deploy:
          name: metawin-deploy-dev-service-management-tournament-eu-west-1
          requires:
            - metawin-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/tournament"
      - deploy:
          name: metawin-deploy-dev-system-tournament-eu-west-1
          requires:
            - metawin-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/tournament"
      # feat-trading
      - deploy-docker:
          name: metawin-deploy-dev-trading-price-producer-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "containers/trading/price-producer"
      - deploy:
          name: metawin-deploy-dev-service-client-trading-eu-west-1
          requires:
            - metawin-deploy-dev-trading-price-producer-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/trading"
      - deploy:
          name: metawin-deploy-dev-service-management-trading-eu-west-1
          requires:
            - metawin-deploy-dev-trading-price-producer-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/trading"
      - deploy:
          name: metawin-deploy-dev-system-trading-eu-west-1
          requires:
            - metawin-deploy-dev-trading-price-producer-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/trading"
      # integr-websockets
      - deploy-docker:
          name: metawin-deploy-dev-websockets-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "containers/websockets"
      # feat-activity
      - deploy:
          name: metawin-deploy-dev-service-client-activity-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/activity"
      # feat-amoe
      - deploy:
          name: metawin-deploy-dev-service-client-amoe-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/amoe"
      - deploy:
          name: metawin-deploy-dev-service-management-amoe-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-amoe-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/amoe"
      - deploy:
          name: metawin-deploy-dev-system-amoe-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-amoe-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/amoe"
      # feat-auth
      - deploy:
          name: metawin-deploy-dev-service-client-auth-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/auth"
      - deploy:
          name: metawin-deploy-dev-service-management-auth-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-auth-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/auth"
      - deploy:
          name: metawin-deploy-dev-system-auth-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-auth-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/auth"
          resource_class: large
      # feat-banking
      - deploy:
          name: metawin-deploy-dev-service-client-banking-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/banking"
      - deploy:
          name: metawin-deploy-dev-service-management-banking-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-banking-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/banking"
      - deploy:
          name: metawin-deploy-dev-system-banking-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-banking-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/banking"
      # feat-blockchain
      - deploy:
          name: metawin-deploy-dev-service-client-blockchain-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/blockchain"
      - deploy:
          name: metawin-deploy-dev-service-management-blockchain-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-blockchain-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/blockchain"
      - deploy:
          name: metawin-deploy-dev-system-blockchain-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-blockchain-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/blockchain"
      # feat-bonus
      - deploy:
          name: metawin-deploy-dev-service-client-bonus-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/bonus"
      - deploy:
          name: metawin-deploy-dev-service-management-bonus-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-bonus-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/bonus"
      - deploy:
          name: metawin-deploy-dev-system-bonus-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-bonus-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/bonus"
      # feat-chat
      - deploy:
          name: metawin-deploy-dev-service-client-chat-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/chat"
      - deploy:
          name: metawin-deploy-dev-system-chat-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-chat-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/chat"
      # feat-contact
      - deploy:
          name: metawin-deploy-dev-service-client-contact-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/contact"
      # feat-coupon
      - deploy:
          name: metawin-deploy-dev-service-client-coupon-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/coupon"
      # feat-deposits
      - deploy:
          name: metawin-deploy-dev-service-client-deposit-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/deposit"
      # feat-games
      - deploy:
          name: metawin-deploy-dev-service-client-game-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/game"
      - deploy:
          name: metawin-deploy-dev-service-management-game-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-game-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/game"
      - deploy:
          name: metawin-deploy-dev-system-game-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-game-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/game"
      # feat-hamburgers
      - deploy:
          name: metawin-deploy-dev-service-client-hamburger-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/hamburger"
      - deploy:
          name: metawin-deploy-dev-service-management-hamburger-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-hamburger-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/hamburger"
      # feat-inventory
      - deploy:
          name: metawin-deploy-dev-service-client-inventory-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/inventory"
      - deploy:
          name: metawin-deploy-dev-service-management-inventory-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-inventory-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/inventory"
      - deploy:
          name: metawin-deploy-dev-system-inventory-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-inventory-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/inventory"
      # feat-jackpot
      - deploy:
          name: metawin-deploy-dev-service-client-jackpot-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/jackpot"
      - deploy:
          name: metawin-deploy-dev-system-jackpot-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-jackpot-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/jackpot"
      # feat-leaderboards
      - deploy:
          name: metawin-deploy-dev-service-client-leaderboard-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/leaderboard"
      - deploy:
          name: metawin-deploy-dev-service-management-leaderboard-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-leaderboard-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/leaderboard"
      - deploy:
          name: metawin-deploy-dev-system-leaderboard-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-leaderboard-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/leaderboard"
      # feat-location
      - deploy:
          name: metawin-deploy-dev-service-client-location-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/location"
      # feat-loyalty
      - deploy:
          name: metawin-deploy-dev-service-client-loyalty-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/loyalty"
      - deploy:
          name: metawin-deploy-dev-service-management-loyalty-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-loyalty-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/loyalty"
      - deploy:
          name: metawin-deploy-dev-system-loyalty-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-loyalty-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/loyalty"
      # feat-markets
      - deploy:
          name: metawin-deploy-dev-service-client-market-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/market"
      # feat-nfts
      - deploy:
          name: metawin-deploy-dev-service-client-nft-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/nft"
      # feat-notifications
      - deploy:
          name: metawin-deploy-dev-service-client-notification-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/notification"
      - deploy:
          name: metawin-deploy-dev-system-notification-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-notification-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/notification"
      # feat-onboarding
      - deploy:
          name: metawin-deploy-dev-service-client-onboarding-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/onboarding"
      - deploy:
          name: metawin-deploy-dev-service-management-onboarding-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-onboarding-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/onboarding"
      # feat-order
      - deploy:
          name: metawin-deploy-dev-service-client-order-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/order"
      - deploy:
          name: metawin-deploy-dev-service-management-order-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-order-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/order"
      - deploy:
          name: metawin-deploy-dev-system-order-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-order-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/order"
      # feat-payments
      - deploy:
          name: metawin-deploy-dev-service-client-payment-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/payment"
      - deploy:
          name: metawin-deploy-dev-service-management-payment-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-payment-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/payment"
      # feat-redeemable
      - deploy:
          name: metawin-deploy-dev-service-client-redeemable-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/redeemable"
      - deploy:
          name: metawin-deploy-dev-service-management-redeemable-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-redeemable-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/redeemable"
      - deploy:
          name: metawin-deploy-dev-system-redeemable-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-redeemable-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/redeemable"
      # feat-referrals
      - deploy:
          name: metawin-deploy-dev-service-client-referral-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/referral"
      - deploy:
          name: metawin-deploy-dev-service-management-referral-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-referral-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/referral"
      - deploy:
          name: metawin-deploy-dev-system-referral-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-referral-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/referral"
      # feat-reward
      - deploy:
          name: metawin-deploy-dev-service-client-reward-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/reward"
      - deploy:
          name: metawin-deploy-dev-system-reward-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-reward-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/reward"
      # feat-social
      - deploy:
          name: metawin-deploy-dev-service-client-social-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/social"
      - deploy:
          name: metawin-deploy-dev-service-management-social-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-social-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/social"
      - deploy:
          name: metawin-deploy-dev-system-social-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-social-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/social"
      # feat-sport
      - deploy:
          name: metawin-deploy-dev-service-client-sport-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/sport"
      - deploy:
          name: metawin-deploy-dev-system-sport-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-sport-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/sport"
      # feat-statistics
      - deploy:
          name: metawin-deploy-dev-service-client-statistics-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/statistics"
      - deploy:
          name: metawin-deploy-dev-service-management-statistics-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-statistics-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/statistics"
      - deploy:
          name: metawin-deploy-dev-system-statistics-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-statistics-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/statistics"
      # feat-store
      - deploy:
          name: metawin-deploy-dev-service-client-store-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/store"
      - deploy:
          name: metawin-deploy-dev-service-management-store-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-store-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/store"
      # feat-support
      - deploy:
          name: metawin-deploy-dev-service-client-support-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/support"
      # feat-sweepstakes
      - deploy:
          name: metawin-deploy-dev-service-client-sweepstake-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/sweepstake"
      - deploy:
          name: metawin-deploy-dev-service-management-sweepstake-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-sweepstake-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/sweepstake"
      - deploy:
          name: metawin-deploy-dev-system-sweepstake-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-sweepstake-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/sweepstake"
      # feat-upgrades
      - deploy:
          name: metawin-deploy-dev-service-client-upgrade-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/upgrade"
      - deploy:
          name: metawin-deploy-dev-service-management-upgrade-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-upgrade-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/upgrade"
      - deploy:
          name: metawin-deploy-dev-system-upgrade-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-upgrade-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/upgrade"
      # feat-users
      - deploy:
          name: metawin-deploy-dev-service-client-user-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/user"
      - deploy:
          name: metawin-deploy-dev-service-management-user-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-user-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/user"
      - deploy:
          name: metawin-deploy-dev-system-activity-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-user-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/activity"
      - deploy:
          name: metawin-deploy-dev-system-user-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-user-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/user"
      # feat-verification
      - deploy:
          name: metawin-deploy-dev-service-client-verification-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/verification"
      - deploy:
          name: metawin-deploy-dev-service-management-verification-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-verification-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/verification"
      - deploy:
          name: metawin-deploy-dev-system-verification-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-verification-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/verification"
      # feat-withdrawals
      - deploy:
          name: metawin-deploy-dev-service-client-withdrawal-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/client/withdrawal"
      - deploy:
          name: metawin-deploy-dev-service-management-withdrawal-eu-west-1
          requires:
            - metawin-deploy-dev-service-client-withdrawal-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/withdrawal"
      # integr-alchemy
      - deploy:
          name: metawin-deploy-dev-service-integration-alchemy-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/alchemy"
      # integr-big-wheel
      - deploy:
          name: metawin-deploy-dev-service-integration-arena-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/arena"
      # integr-betby
      - deploy:
          name: metawin-deploy-dev-service-integration-betby-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/betby"
      # integr-blocknative
      - deploy:
          name: metawin-deploy-dev-service-integration-blocknative-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/blocknative"
      # integr-bloomreach
      - deploy:
          name: metawin-deploy-dev-service-integration-bloomreach-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/bloomreach"
      # integr-crash
      - deploy:
          name: metawin-deploy-dev-service-integration-crash-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/crash"
      # integr-discord
      - deploy:
          name: metawin-deploy-dev-service-integration-discord-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/discord"
      # integr-evolution
      - deploy:
          name: metawin-deploy-dev-service-integration-evolution-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/evolution"
      # integr-fireblocks
      - deploy:
          name: metawin-deploy-dev-service-integration-fireblocks-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/fireblocks"
      # integr-gametech
      - deploy:
          name: metawin-deploy-dev-service-integration-gametech-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/gametech"
      # integr-helius
      - deploy:
          name: metawin-deploy-dev-service-integration-helius-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/helius"
      # integr-hilo
      - deploy:
          name: metawin-deploy-dev-service-integration-hilo-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/hilo"
      # integr-hub88
      - deploy:
          name: metawin-deploy-dev-service-integration-hub88-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/hub88"
      # integr-influencer
      - deploy:
          name: metawin-deploy-dev-service-integration-influencer-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/influencer"
      # integr-playSite
      - deploy:
          name: metawin-deploy-dev-service-integration-play-site-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/play-site"
      # integr-pragmaticplay
      - deploy:
          name: metawin-deploy-dev-service-integration-pragmaticplay-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/pragmaticplay"
      # integr-rubyplay
      - deploy:
          name: metawin-deploy-dev-service-integration-rubyplay-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/rubyplay"
      # integr-skywind
      - deploy:
          name: metawin-deploy-dev-service-integration-skywind-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/skywind"
      # integr-softswiss
      - deploy:
          name: metawin-deploy-dev-service-integration-softswiss-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/softswiss"
      # integr-tada
      - deploy:
          name: metawin-deploy-dev-service-integration-tada-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/tada"
      # integr-tambola
      - deploy:
          name: metawin-deploy-dev-service-integration-tambola-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/tambola"
      # integr-tequity
      - deploy:
          name: metawin-deploy-dev-service-integration-tequity-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/tequity"
      # integr-veriff
      - deploy:
          name: metawin-deploy-dev-service-integration-veriff-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/integration/veriff"
      # feat-admin
      - deploy:
          name: metawin-deploy-dev-service-management-admin-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/admin"
      - deploy:
          name: metawin-deploy-dev-system-admin-eu-west-1
          requires:
            - metawin-deploy-dev-service-management-admin-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/admin"
      # feat-coupons
      - deploy:
          name: metawin-deploy-dev-service-management-coupon-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/coupon"
      # feat-deposit
      - deploy:
          name: metawin-deploy-dev-service-management-deposit-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/deposit"
      - deploy:
          name: metawin-deploy-dev-system-deposit-eu-west-1
          requires:
            - metawin-deploy-dev-service-management-deposit-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/deposit"
      # feat-integrations
      - deploy:
          name: metawin-deploy-dev-service-management-integration-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/integration"
      # feat-jackpots
      - deploy:
          name: metawin-deploy-dev-service-management-jackpot-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/jackpot"
      # feat-nft
      - deploy:
          name: metawin-deploy-dev-service-management-nft-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/nft"
      - deploy:
          name: metawin-deploy-dev-system-nft-eu-west-1
          requires:
            - metawin-deploy-dev-service-management-nft-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/nft"
      # feat-operations
      - deploy:
          name: metawin-deploy-dev-service-management-operations-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/operations"
      - deploy:
          name: metawin-deploy-dev-system-operations-eu-west-1
          requires:
            - metawin-deploy-dev-service-management-operations-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/operations"
          resource_class: large
      # feat-rewards
      - deploy:
          name: metawin-deploy-dev-service-management-reward-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/reward"
      # feat-sports
      - deploy:
          name: metawin-deploy-dev-service-management-sport-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/sport"
      # feat-subscriptions
      - deploy:
          name: metawin-deploy-dev-service-management-subscription-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/subscription"
      # feat-sync
      - deploy:
          name: metawin-deploy-dev-service-management-sync-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "services/management/sync"
      # feat-alchemy
      - deploy:
          name: metawin-deploy-dev-system-alchemy-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/alchemy"
      # feat-blocknative
      - deploy:
          name: metawin-deploy-dev-system-blocknative-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/blocknative"
      # feat-crm
      - deploy:
          name: metawin-deploy-dev-system-crm-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/crm"
      # feat-discord
      - deploy:
          name: metawin-deploy-dev-system-discord-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/discord"
      # feat-hamburger
      - deploy:
          name: metawin-deploy-dev-system-hamburger-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/hamburger"
      # feat-payment
      - deploy:
          name: metawin-deploy-dev-system-payment-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/payment"
      # feat-slack
      - deploy:
          name: metawin-deploy-dev-system-slack-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/slack"
      # feat-twitter
      - deploy:
          name: metawin-deploy-dev-system-twitter-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/twitter"
      # feat-utilities
      - deploy:
          name: metawin-deploy-dev-system-utility-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/utility"
      # feat-websockets
      - deploy:
          name: metawin-deploy-dev-system-websockets-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/websockets"
      # feat-withdrawal
      - deploy:
          name: metawin-deploy-dev-system-withdrawal-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "system/withdrawal"
      # feat-testing
      - deploy-docker:
          name: metawin-deploy-dev-testing-bots-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "testing/bots"
      - deploy:
          name: metawin-deploy-dev-testing-handlers-eu-west-1
          requires:
            - metawin-deploy-dev-testing-bots-eu-west-1
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          location: "testing/handlers"
      # Docs
      - deploy-docs:
          name: metawin-deploy-mgnt-docs-dev-eu-west-1
          requires:
              - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          api: management
          resource_class: xlarge
      - deploy-docs:
          name: metawin-deploy-client-docs-dev-eu-west-1
          requires:
              - build-deploy
          filters:
            branches:
              only: dev
          context: platform-metawin-dev
          region: eu-west-1
          brand: metawin
          stage: dev
          api: client
          resource_class: xlarge
      # DEPLOY prod - eu-west-1
      # feat-tournaments
      - deploy-docker:
          name: metawin-deploy-prod-tournament-engine-bingo-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "containers/tournament/engine/bingo"
      - deploy-docker:
          name: metawin-deploy-prod-tournament-engine-crash-eu-west-1
          requires:
            - metawin-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "containers/tournament/engine/crash"
      - deploy-docker:
          name: metawin-deploy-prod-tournament-engine-hilo-eu-west-1
          requires:
            - metawin-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "containers/tournament/engine/hilo"
      - deploy-docker:
          name: metawin-deploy-prod-tournament-engine-slot-eu-west-1
          requires:
            - metawin-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "containers/tournament/engine/slot"
      - deploy:
          name: metawin-deploy-prod-service-client-tournament-eu-west-1
          requires:
            - metawin-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/tournament"
      - deploy:
          name: metawin-deploy-prod-service-management-tournament-eu-west-1
          requires:
            - metawin-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/tournament"
      - deploy:
          name: metawin-deploy-prod-system-tournament-eu-west-1
          requires:
            - metawin-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/tournament"
      # feat-trading
      - deploy-docker:
          name: metawin-deploy-prod-trading-price-producer-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "containers/trading/price-producer"
      - deploy:
          name: metawin-deploy-prod-service-client-trading-eu-west-1
          requires:
            - metawin-deploy-prod-trading-price-producer-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/trading"
      - deploy:
          name: metawin-deploy-prod-service-management-trading-eu-west-1
          requires:
            - metawin-deploy-prod-trading-price-producer-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/trading"
      - deploy:
          name: metawin-deploy-prod-system-trading-eu-west-1
          requires:
            - metawin-deploy-prod-trading-price-producer-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/trading"
      # integr-websockets
      - deploy-docker:
          name: metawin-deploy-prod-websockets-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "containers/websockets"
      # feat-activity
      - deploy:
          name: metawin-deploy-prod-service-client-activity-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/activity"
      # feat-amoe
      - deploy:
          name: metawin-deploy-prod-service-client-amoe-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/amoe"
      - deploy:
          name: metawin-deploy-prod-service-management-amoe-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-amoe-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/amoe"
      - deploy:
          name: metawin-deploy-prod-system-amoe-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-amoe-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/amoe"
      # feat-auth
      - deploy:
          name: metawin-deploy-prod-service-client-auth-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/auth"
      - deploy:
          name: metawin-deploy-prod-service-management-auth-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-auth-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/auth"
      - deploy:
          name: metawin-deploy-prod-system-auth-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-auth-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/auth"
          resource_class: large
      # feat-banking
      - deploy:
          name: metawin-deploy-prod-service-client-banking-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/banking"
      - deploy:
          name: metawin-deploy-prod-service-management-banking-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-banking-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/banking"
      - deploy:
          name: metawin-deploy-prod-system-banking-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-banking-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/banking"
      # feat-blockchain
      - deploy:
          name: metawin-deploy-prod-service-client-blockchain-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/blockchain"
      - deploy:
          name: metawin-deploy-prod-service-management-blockchain-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-blockchain-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/blockchain"
      - deploy:
          name: metawin-deploy-prod-system-blockchain-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-blockchain-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/blockchain"
      # feat-bonus
      - deploy:
          name: metawin-deploy-prod-service-client-bonus-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/bonus"
      - deploy:
          name: metawin-deploy-prod-service-management-bonus-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-bonus-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/bonus"
      - deploy:
          name: metawin-deploy-prod-system-bonus-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-bonus-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/bonus"
      # feat-chat
      - deploy:
          name: metawin-deploy-prod-service-client-chat-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/chat"
      - deploy:
          name: metawin-deploy-prod-system-chat-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-chat-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/chat"
      # feat-contact
      - deploy:
          name: metawin-deploy-prod-service-client-contact-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/contact"
      # feat-coupon
      - deploy:
          name: metawin-deploy-prod-service-client-coupon-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/coupon"
      # feat-deposits
      - deploy:
          name: metawin-deploy-prod-service-client-deposit-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/deposit"
      # feat-games
      - deploy:
          name: metawin-deploy-prod-service-client-game-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/game"
      - deploy:
          name: metawin-deploy-prod-service-management-game-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-game-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/game"
      - deploy:
          name: metawin-deploy-prod-system-game-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-game-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/game"
      # feat-hamburgers
      - deploy:
          name: metawin-deploy-prod-service-client-hamburger-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/hamburger"
      - deploy:
          name: metawin-deploy-prod-service-management-hamburger-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-hamburger-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/hamburger"
      # feat-inventory
      - deploy:
          name: metawin-deploy-prod-service-client-inventory-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/inventory"
      - deploy:
          name: metawin-deploy-prod-service-management-inventory-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-inventory-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/inventory"
      - deploy:
          name: metawin-deploy-prod-system-inventory-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-inventory-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/inventory"
      # feat-jackpot
      - deploy:
          name: metawin-deploy-prod-service-client-jackpot-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/jackpot"
      - deploy:
          name: metawin-deploy-prod-system-jackpot-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-jackpot-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/jackpot"
      # feat-leaderboards
      - deploy:
          name: metawin-deploy-prod-service-client-leaderboard-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/leaderboard"
      - deploy:
          name: metawin-deploy-prod-service-management-leaderboard-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-leaderboard-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/leaderboard"
      - deploy:
          name: metawin-deploy-prod-system-leaderboard-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-leaderboard-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/leaderboard"
      # feat-location
      - deploy:
          name: metawin-deploy-prod-service-client-location-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/location"
      # feat-loyalty
      - deploy:
          name: metawin-deploy-prod-service-client-loyalty-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/loyalty"
      - deploy:
          name: metawin-deploy-prod-service-management-loyalty-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-loyalty-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/loyalty"
      - deploy:
          name: metawin-deploy-prod-system-loyalty-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-loyalty-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/loyalty"
      # feat-markets
      - deploy:
          name: metawin-deploy-prod-service-client-market-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/market"
      # feat-nfts
      - deploy:
          name: metawin-deploy-prod-service-client-nft-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/nft"
      # feat-notifications
      - deploy:
          name: metawin-deploy-prod-service-client-notification-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/notification"
      - deploy:
          name: metawin-deploy-prod-system-notification-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-notification-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/notification"
      # feat-onboarding
      - deploy:
          name: metawin-deploy-prod-service-client-onboarding-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/onboarding"
      - deploy:
          name: metawin-deploy-prod-service-management-onboarding-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-onboarding-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/onboarding"
      # feat-order
      - deploy:
          name: metawin-deploy-prod-service-client-order-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/order"
      - deploy:
          name: metawin-deploy-prod-service-management-order-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-order-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/order"
      - deploy:
          name: metawin-deploy-prod-system-order-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-order-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/order"
      # feat-payments
      - deploy:
          name: metawin-deploy-prod-service-client-payment-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/payment"
      - deploy:
          name: metawin-deploy-prod-service-management-payment-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-payment-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/payment"
      # feat-redeemable
      - deploy:
          name: metawin-deploy-prod-service-client-redeemable-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/redeemable"
      - deploy:
          name: metawin-deploy-prod-service-management-redeemable-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-redeemable-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/redeemable"
      - deploy:
          name: metawin-deploy-prod-system-redeemable-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-redeemable-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/redeemable"
      # feat-referrals
      - deploy:
          name: metawin-deploy-prod-service-client-referral-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/referral"
      - deploy:
          name: metawin-deploy-prod-service-management-referral-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-referral-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/referral"
      - deploy:
          name: metawin-deploy-prod-system-referral-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-referral-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/referral"
      # feat-reward
      - deploy:
          name: metawin-deploy-prod-service-client-reward-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/reward"
      - deploy:
          name: metawin-deploy-prod-system-reward-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-reward-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/reward"
      # feat-social
      - deploy:
          name: metawin-deploy-prod-service-client-social-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/social"
      - deploy:
          name: metawin-deploy-prod-service-management-social-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-social-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/social"
      - deploy:
          name: metawin-deploy-prod-system-social-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-social-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/social"
      # feat-sport
      - deploy:
          name: metawin-deploy-prod-service-client-sport-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/sport"
      - deploy:
          name: metawin-deploy-prod-system-sport-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-sport-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/sport"
      # feat-statistics
      - deploy:
          name: metawin-deploy-prod-service-client-statistics-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/statistics"
      - deploy:
          name: metawin-deploy-prod-service-management-statistics-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-statistics-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/statistics"
      - deploy:
          name: metawin-deploy-prod-system-statistics-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-statistics-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/statistics"
      # feat-store
      - deploy:
          name: metawin-deploy-prod-service-client-store-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/store"
      - deploy:
          name: metawin-deploy-prod-service-management-store-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-store-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/store"
      # feat-support
      - deploy:
          name: metawin-deploy-prod-service-client-support-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/support"
      # feat-sweepstakes
      - deploy:
          name: metawin-deploy-prod-service-client-sweepstake-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/sweepstake"
      - deploy:
          name: metawin-deploy-prod-service-management-sweepstake-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-sweepstake-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/sweepstake"
      - deploy:
          name: metawin-deploy-prod-system-sweepstake-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-sweepstake-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/sweepstake"
      # feat-upgrades
      - deploy:
          name: metawin-deploy-prod-service-client-upgrade-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/upgrade"
      - deploy:
          name: metawin-deploy-prod-service-management-upgrade-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-upgrade-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/upgrade"
      - deploy:
          name: metawin-deploy-prod-system-upgrade-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-upgrade-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/upgrade"
      # feat-users
      - deploy:
          name: metawin-deploy-prod-service-client-user-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/user"
      - deploy:
          name: metawin-deploy-prod-service-management-user-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-user-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/user"
      - deploy:
          name: metawin-deploy-prod-system-activity-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-user-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/activity"
      - deploy:
          name: metawin-deploy-prod-system-user-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-user-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/user"
      # feat-verification
      - deploy:
          name: metawin-deploy-prod-service-client-verification-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/verification"
      - deploy:
          name: metawin-deploy-prod-service-management-verification-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-verification-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/verification"
      - deploy:
          name: metawin-deploy-prod-system-verification-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-verification-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/verification"
      # feat-withdrawals
      - deploy:
          name: metawin-deploy-prod-service-client-withdrawal-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/client/withdrawal"
      - deploy:
          name: metawin-deploy-prod-service-management-withdrawal-eu-west-1
          requires:
            - metawin-deploy-prod-service-client-withdrawal-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/withdrawal"
      # integr-alchemy
      - deploy:
          name: metawin-deploy-prod-service-integration-alchemy-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/alchemy"
      # integr-big-wheel
      - deploy:
          name: metawin-deploy-prod-service-integration-arena-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/arena"
      # integr-betby
      - deploy:
          name: metawin-deploy-prod-service-integration-betby-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/betby"
      # integr-blocknative
      - deploy:
          name: metawin-deploy-prod-service-integration-blocknative-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/blocknative"
      # integr-bloomreach
      - deploy:
          name: metawin-deploy-prod-service-integration-bloomreach-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/bloomreach"
      # integr-crash
      - deploy:
          name: metawin-deploy-prod-service-integration-crash-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/crash"
      # integr-discord
      - deploy:
          name: metawin-deploy-prod-service-integration-discord-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/discord"
      # integr-evolution
      - deploy:
          name: metawin-deploy-prod-service-integration-evolution-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/evolution"
      # integr-fireblocks
      - deploy:
          name: metawin-deploy-prod-service-integration-fireblocks-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/fireblocks"
      # integr-gametech
      - deploy:
          name: metawin-deploy-prod-service-integration-gametech-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/gametech"
      # integr-helius
      - deploy:
          name: metawin-deploy-prod-service-integration-helius-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/helius"
      # integr-hilo
      - deploy:
          name: metawin-deploy-prod-service-integration-hilo-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/hilo"
      # integr-hub88
      - deploy:
          name: metawin-deploy-prod-service-integration-hub88-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/hub88"
      # integr-influencer
      - deploy:
          name: metawin-deploy-prod-service-integration-influencer-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/influencer"
      # integr-playSite
      - deploy:
          name: metawin-deploy-prod-service-integration-play-site-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/play-site"
      # integr-pragmaticplay
      - deploy:
          name: metawin-deploy-prod-service-integration-pragmaticplay-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/pragmaticplay"
      # integr-rubyplay
      - deploy:
          name: metawin-deploy-prod-service-integration-rubyplay-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/rubyplay"
      # integr-skywind
      - deploy:
          name: metawin-deploy-prod-service-integration-skywind-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/skywind"
      # integr-softswiss
      - deploy:
          name: metawin-deploy-prod-service-integration-softswiss-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/softswiss"
      # integr-tada
      - deploy:
          name: metawin-deploy-prod-service-integration-tada-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/tada"
      # integr-tambola
      - deploy:
          name: metawin-deploy-prod-service-integration-tambola-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/tambola"
      # integr-tequity
      - deploy:
          name: metawin-deploy-prod-service-integration-tequity-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/tequity"
      # integr-veriff
      - deploy:
          name: metawin-deploy-prod-service-integration-veriff-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/integration/veriff"
      # feat-admin
      - deploy:
          name: metawin-deploy-prod-service-management-admin-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/admin"
      - deploy:
          name: metawin-deploy-prod-system-admin-eu-west-1
          requires:
            - metawin-deploy-prod-service-management-admin-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/admin"
      # feat-coupons
      - deploy:
          name: metawin-deploy-prod-service-management-coupon-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/coupon"
      # feat-deposit
      - deploy:
          name: metawin-deploy-prod-service-management-deposit-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/deposit"
      - deploy:
          name: metawin-deploy-prod-system-deposit-eu-west-1
          requires:
            - metawin-deploy-prod-service-management-deposit-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/deposit"
      # feat-integrations
      - deploy:
          name: metawin-deploy-prod-service-management-integration-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/integration"
      # feat-jackpots
      - deploy:
          name: metawin-deploy-prod-service-management-jackpot-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/jackpot"
      # feat-nft
      - deploy:
          name: metawin-deploy-prod-service-management-nft-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/nft"
      - deploy:
          name: metawin-deploy-prod-system-nft-eu-west-1
          requires:
            - metawin-deploy-prod-service-management-nft-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/nft"
      # feat-operations
      - deploy:
          name: metawin-deploy-prod-service-management-operations-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/operations"
      - deploy:
          name: metawin-deploy-prod-system-operations-eu-west-1
          requires:
            - metawin-deploy-prod-service-management-operations-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/operations"
          resource_class: large
      # feat-rewards
      - deploy:
          name: metawin-deploy-prod-service-management-reward-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/reward"
      # feat-sports
      - deploy:
          name: metawin-deploy-prod-service-management-sport-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/sport"
      # feat-subscriptions
      - deploy:
          name: metawin-deploy-prod-service-management-subscription-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/subscription"
      # feat-sync
      - deploy:
          name: metawin-deploy-prod-service-management-sync-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "services/management/sync"
      # feat-alchemy
      - deploy:
          name: metawin-deploy-prod-system-alchemy-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/alchemy"
      # feat-blocknative
      - deploy:
          name: metawin-deploy-prod-system-blocknative-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/blocknative"
      # feat-crm
      - deploy:
          name: metawin-deploy-prod-system-crm-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/crm"
      # feat-discord
      - deploy:
          name: metawin-deploy-prod-system-discord-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/discord"
      # feat-hamburger
      - deploy:
          name: metawin-deploy-prod-system-hamburger-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/hamburger"
      # feat-payment
      - deploy:
          name: metawin-deploy-prod-system-payment-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/payment"
      # feat-slack
      - deploy:
          name: metawin-deploy-prod-system-slack-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/slack"
      # feat-twitter
      - deploy:
          name: metawin-deploy-prod-system-twitter-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/twitter"
      # feat-utilities
      - deploy:
          name: metawin-deploy-prod-system-utility-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/utility"
      # feat-websockets
      - deploy:
          name: metawin-deploy-prod-system-websockets-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/websockets"
      # feat-withdrawal
      - deploy:
          name: metawin-deploy-prod-system-withdrawal-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "system/withdrawal"
      # feat-testing
      - deploy-docker:
          name: metawin-deploy-prod-testing-bots-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "testing/bots"
      - deploy:
          name: metawin-deploy-prod-testing-handlers-eu-west-1
          requires:
            - metawin-deploy-prod-testing-bots-eu-west-1
          filters:
            branches:
              only: master
          context: platform-metawin-prod
          region: eu-west-1
          brand: metawin
          stage: prod
          location: "testing/handlers"

      # Brand: MetaWin US
      # DEPLOY dev - eu-west-1
      # feat-tournaments
      - deploy-docker:
          name: metawin-us-deploy-dev-tournament-engine-bingo-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "containers/tournament/engine/bingo"
      - deploy-docker:
          name: metawin-us-deploy-dev-tournament-engine-crash-eu-west-1
          requires:
            - metawin-us-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "containers/tournament/engine/crash"
      - deploy-docker:
          name: metawin-us-deploy-dev-tournament-engine-hilo-eu-west-1
          requires:
            - metawin-us-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "containers/tournament/engine/hilo"
      - deploy-docker:
          name: metawin-us-deploy-dev-tournament-engine-slot-eu-west-1
          requires:
            - metawin-us-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "containers/tournament/engine/slot"
      - deploy:
          name: metawin-us-deploy-dev-service-client-tournament-eu-west-1
          requires:
            - metawin-us-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/tournament"
      - deploy:
          name: metawin-us-deploy-dev-service-management-tournament-eu-west-1
          requires:
            - metawin-us-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/tournament"
      - deploy:
          name: metawin-us-deploy-dev-system-tournament-eu-west-1
          requires:
            - metawin-us-deploy-dev-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/tournament"
      # integr-websockets
      - deploy-docker:
          name: metawin-us-deploy-dev-websockets-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "containers/websockets"
      # feat-activity
      - deploy:
          name: metawin-us-deploy-dev-service-client-activity-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/activity"
      # feat-amoe
      - deploy:
          name: metawin-us-deploy-dev-service-client-amoe-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/amoe"
      - deploy:
          name: metawin-us-deploy-dev-service-management-amoe-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-amoe-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/amoe"
      - deploy:
          name: metawin-us-deploy-dev-system-amoe-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-amoe-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/amoe"
      # feat-auth
      - deploy:
          name: metawin-us-deploy-dev-service-client-auth-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/auth"
      - deploy:
          name: metawin-us-deploy-dev-service-management-auth-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-auth-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/auth"
      - deploy:
          name: metawin-us-deploy-dev-system-auth-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-auth-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/auth"
          resource_class: large
      # feat-banking
      - deploy:
          name: metawin-us-deploy-dev-service-client-banking-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/banking"
      - deploy:
          name: metawin-us-deploy-dev-service-management-banking-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-banking-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/banking"
      - deploy:
          name: metawin-us-deploy-dev-system-banking-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-banking-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/banking"
      # feat-blockchain
      - deploy:
          name: metawin-us-deploy-dev-service-client-blockchain-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/blockchain"
      - deploy:
          name: metawin-us-deploy-dev-service-management-blockchain-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-blockchain-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/blockchain"
      - deploy:
          name: metawin-us-deploy-dev-system-blockchain-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-blockchain-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/blockchain"
      # feat-bonus
      - deploy:
          name: metawin-us-deploy-dev-service-client-bonus-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/bonus"
      - deploy:
          name: metawin-us-deploy-dev-service-management-bonus-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-bonus-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/bonus"
      - deploy:
          name: metawin-us-deploy-dev-system-bonus-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-bonus-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/bonus"
      # feat-chat
      - deploy:
          name: metawin-us-deploy-dev-service-client-chat-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/chat"
      - deploy:
          name: metawin-us-deploy-dev-system-chat-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-chat-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/chat"
      # feat-contact
      - deploy:
          name: metawin-us-deploy-dev-service-client-contact-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/contact"
      # feat-coupon
      - deploy:
          name: metawin-us-deploy-dev-service-client-coupon-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/coupon"
      # feat-deposits
      - deploy:
          name: metawin-us-deploy-dev-service-client-deposit-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/deposit"
      # feat-games
      - deploy:
          name: metawin-us-deploy-dev-service-client-game-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/game"
      - deploy:
          name: metawin-us-deploy-dev-service-management-game-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-game-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/game"
      - deploy:
          name: metawin-us-deploy-dev-system-game-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-game-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/game"
      # feat-hamburgers
      - deploy:
          name: metawin-us-deploy-dev-service-client-hamburger-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/hamburger"
      - deploy:
          name: metawin-us-deploy-dev-service-management-hamburger-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-hamburger-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/hamburger"
      # feat-inventory
      - deploy:
          name: metawin-us-deploy-dev-service-client-inventory-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/inventory"
      - deploy:
          name: metawin-us-deploy-dev-service-management-inventory-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-inventory-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/inventory"
      - deploy:
          name: metawin-us-deploy-dev-system-inventory-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-inventory-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/inventory"
      # feat-jackpot
      - deploy:
          name: metawin-us-deploy-dev-service-client-jackpot-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/jackpot"
      - deploy:
          name: metawin-us-deploy-dev-system-jackpot-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-jackpot-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/jackpot"
      # feat-leaderboards
      - deploy:
          name: metawin-us-deploy-dev-service-client-leaderboard-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/leaderboard"
      - deploy:
          name: metawin-us-deploy-dev-service-management-leaderboard-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-leaderboard-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/leaderboard"
      - deploy:
          name: metawin-us-deploy-dev-system-leaderboard-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-leaderboard-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/leaderboard"
      # feat-location
      - deploy:
          name: metawin-us-deploy-dev-service-client-location-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/location"
      # feat-loyalty
      - deploy:
          name: metawin-us-deploy-dev-service-client-loyalty-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/loyalty"
      - deploy:
          name: metawin-us-deploy-dev-service-management-loyalty-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-loyalty-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/loyalty"
      - deploy:
          name: metawin-us-deploy-dev-system-loyalty-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-loyalty-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/loyalty"
      # feat-nfts
      - deploy:
          name: metawin-us-deploy-dev-service-client-nft-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/nft"
      # feat-notifications
      - deploy:
          name: metawin-us-deploy-dev-service-client-notification-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/notification"
      - deploy:
          name: metawin-us-deploy-dev-system-notification-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-notification-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/notification"
      # feat-onboarding
      - deploy:
          name: metawin-us-deploy-dev-service-client-onboarding-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/onboarding"
      - deploy:
          name: metawin-us-deploy-dev-service-management-onboarding-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-onboarding-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/onboarding"
      # feat-order
      - deploy:
          name: metawin-us-deploy-dev-service-client-order-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/order"
      - deploy:
          name: metawin-us-deploy-dev-service-management-order-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-order-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/order"
      - deploy:
          name: metawin-us-deploy-dev-system-order-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-order-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/order"
      # feat-payments
      - deploy:
          name: metawin-us-deploy-dev-service-client-payment-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/payment"
      - deploy:
          name: metawin-us-deploy-dev-service-management-payment-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-payment-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/payment"
      # feat-redeemable
      - deploy:
          name: metawin-us-deploy-dev-service-client-redeemable-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/redeemable"
      - deploy:
          name: metawin-us-deploy-dev-service-management-redeemable-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-redeemable-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/redeemable"
      - deploy:
          name: metawin-us-deploy-dev-system-redeemable-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-redeemable-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/redeemable"
      # feat-referrals
      - deploy:
          name: metawin-us-deploy-dev-service-client-referral-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/referral"
      - deploy:
          name: metawin-us-deploy-dev-service-management-referral-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-referral-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/referral"
      - deploy:
          name: metawin-us-deploy-dev-system-referral-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-referral-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/referral"
      # feat-reward
      - deploy:
          name: metawin-us-deploy-dev-service-client-reward-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/reward"
      - deploy:
          name: metawin-us-deploy-dev-system-reward-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-reward-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/reward"
      # feat-social
      - deploy:
          name: metawin-us-deploy-dev-service-client-social-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/social"
      - deploy:
          name: metawin-us-deploy-dev-service-management-social-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-social-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/social"
      - deploy:
          name: metawin-us-deploy-dev-system-social-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-social-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/social"
      # feat-statistics
      - deploy:
          name: metawin-us-deploy-dev-service-client-statistics-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/statistics"
      - deploy:
          name: metawin-us-deploy-dev-service-management-statistics-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-statistics-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/statistics"
      - deploy:
          name: metawin-us-deploy-dev-system-statistics-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-statistics-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/statistics"
      # feat-store
      - deploy:
          name: metawin-us-deploy-dev-service-client-store-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/store"
      - deploy:
          name: metawin-us-deploy-dev-service-management-store-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-store-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/store"
      # feat-support
      - deploy:
          name: metawin-us-deploy-dev-service-client-support-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/support"
      # feat-sweepstakes
      - deploy:
          name: metawin-us-deploy-dev-service-client-sweepstake-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/sweepstake"
      - deploy:
          name: metawin-us-deploy-dev-service-management-sweepstake-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-sweepstake-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/sweepstake"
      - deploy:
          name: metawin-us-deploy-dev-system-sweepstake-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-sweepstake-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/sweepstake"
      # feat-upgrades
      - deploy:
          name: metawin-us-deploy-dev-service-client-upgrade-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/upgrade"
      - deploy:
          name: metawin-us-deploy-dev-service-management-upgrade-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-upgrade-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/upgrade"
      - deploy:
          name: metawin-us-deploy-dev-system-upgrade-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-upgrade-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/upgrade"
      # feat-users
      - deploy:
          name: metawin-us-deploy-dev-service-client-user-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/user"
      - deploy:
          name: metawin-us-deploy-dev-service-management-user-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-user-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/user"
      - deploy:
          name: metawin-us-deploy-dev-system-activity-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-user-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/activity"
      - deploy:
          name: metawin-us-deploy-dev-system-user-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-user-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/user"
      # feat-verification
      - deploy:
          name: metawin-us-deploy-dev-service-client-verification-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/verification"
      - deploy:
          name: metawin-us-deploy-dev-service-management-verification-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-verification-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/verification"
      - deploy:
          name: metawin-us-deploy-dev-system-verification-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-verification-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/verification"
      # feat-withdrawals
      - deploy:
          name: metawin-us-deploy-dev-service-client-withdrawal-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/client/withdrawal"
      - deploy:
          name: metawin-us-deploy-dev-service-management-withdrawal-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-client-withdrawal-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/withdrawal"
      # integr-alchemy
      - deploy:
          name: metawin-us-deploy-dev-service-integration-alchemy-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/alchemy"
      # integr-big-wheel
      - deploy:
          name: metawin-us-deploy-dev-service-integration-arena-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/arena"
      # integr-blocknative
      - deploy:
          name: metawin-us-deploy-dev-service-integration-blocknative-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/blocknative"
      # integr-bloomreach
      - deploy:
          name: metawin-us-deploy-dev-service-integration-bloomreach-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/bloomreach"
      # integr-crash
      - deploy:
          name: metawin-us-deploy-dev-service-integration-crash-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/crash"
      # integr-discord
      - deploy:
          name: metawin-us-deploy-dev-service-integration-discord-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/discord"
      # integr-evolution
      - deploy:
          name: metawin-us-deploy-dev-service-integration-evolution-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/evolution"
      # integr-fireblocks
      - deploy:
          name: metawin-us-deploy-dev-service-integration-fireblocks-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/fireblocks"
      # integr-gametech
      - deploy:
          name: metawin-us-deploy-dev-service-integration-gametech-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/gametech"
      # integr-helius
      - deploy:
          name: metawin-us-deploy-dev-service-integration-helius-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/helius"
      # integr-hub88
      - deploy:
          name: metawin-us-deploy-dev-service-integration-hub88-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/hub88"
      # integr-influencer
      - deploy:
          name: metawin-us-deploy-dev-service-integration-influencer-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/influencer"
      # integr-pragmaticplay
      - deploy:
          name: metawin-us-deploy-dev-service-integration-pragmaticplay-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/pragmaticplay"
      # integr-rubyplay
      - deploy:
          name: metawin-us-deploy-dev-service-integration-rubyplay-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/rubyplay"
      # integr-skywind
      - deploy:
          name: metawin-us-deploy-dev-service-integration-skywind-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/skywind"
      # integr-softswiss
      - deploy:
          name: metawin-us-deploy-dev-service-integration-softswiss-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/softswiss"
      # integr-tada
      - deploy:
          name: metawin-us-deploy-dev-service-integration-tada-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/tada"
      # integr-tequity
      - deploy:
          name: metawin-us-deploy-dev-service-integration-tequity-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/tequity"
      # integr-veriff
      - deploy:
          name: metawin-us-deploy-dev-service-integration-veriff-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/integration/veriff"
      # feat-admin
      - deploy:
          name: metawin-us-deploy-dev-service-management-admin-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/admin"
      - deploy:
          name: metawin-us-deploy-dev-system-admin-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-management-admin-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/admin"
      # feat-coupons
      - deploy:
          name: metawin-us-deploy-dev-service-management-coupon-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/coupon"
      # feat-deposit
      - deploy:
          name: metawin-us-deploy-dev-service-management-deposit-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/deposit"
      - deploy:
          name: metawin-us-deploy-dev-system-deposit-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-management-deposit-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/deposit"
      # feat-integrations
      - deploy:
          name: metawin-us-deploy-dev-service-management-integration-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/integration"
      # feat-jackpots
      - deploy:
          name: metawin-us-deploy-dev-service-management-jackpot-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/jackpot"
      # feat-nft
      - deploy:
          name: metawin-us-deploy-dev-service-management-nft-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/nft"
      - deploy:
          name: metawin-us-deploy-dev-system-nft-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-management-nft-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/nft"
      # feat-operations
      - deploy:
          name: metawin-us-deploy-dev-service-management-operations-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/operations"
      - deploy:
          name: metawin-us-deploy-dev-system-operations-eu-west-1
          requires:
            - metawin-us-deploy-dev-service-management-operations-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/operations"
          resource_class: large
      # feat-rewards
      - deploy:
          name: metawin-us-deploy-dev-service-management-reward-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/reward"
      # feat-sports
      - deploy:
          name: metawin-us-deploy-dev-service-management-sport-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/sport"
      # feat-sync
      - deploy:
          name: metawin-us-deploy-dev-service-management-sync-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "services/management/sync"
      # feat-alchemy
      - deploy:
          name: metawin-us-deploy-dev-system-alchemy-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/alchemy"
      # feat-blocknative
      - deploy:
          name: metawin-us-deploy-dev-system-blocknative-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/blocknative"
      # feat-crm
      - deploy:
          name: metawin-us-deploy-dev-system-crm-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/crm"
      # feat-hamburger
      - deploy:
          name: metawin-us-deploy-dev-system-hamburger-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/hamburger"
      # feat-payment
      - deploy:
          name: metawin-us-deploy-dev-system-payment-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/payment"
      # feat-slack
      - deploy:
          name: metawin-us-deploy-dev-system-slack-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/slack"
      # feat-twitter
      - deploy:
          name: metawin-us-deploy-dev-system-twitter-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/twitter"
      # feat-utilities
      - deploy:
          name: metawin-us-deploy-dev-system-utility-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/utility"
      # feat-websockets
      - deploy:
          name: metawin-us-deploy-dev-system-websockets-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/websockets"
      # feat-withdrawal
      - deploy:
          name: metawin-us-deploy-dev-system-withdrawal-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "system/withdrawal"
      # feat-testing
      - deploy-docker:
          name: metawin-us-deploy-dev-testing-bots-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "testing/bots"
      - deploy:
          name: metawin-us-deploy-dev-testing-handlers-eu-west-1
          requires:
            - metawin-us-deploy-dev-testing-bots-eu-west-1
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          location: "testing/handlers"
      # Docs
      - deploy-docs:
          name: metawin-us-deploy-mgnt-docs-dev-eu-west-1
          requires:
              - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          api: management
          resource_class: xlarge
      - deploy-docs:
          name: metawin-us-deploy-client-docs-dev-eu-west-1
          requires:
              - build-deploy
          filters:
            branches:
              only: release/metawin-us/dev
          context: platform-metawin-us-dev
          region: eu-west-1
          brand: metawin-us
          stage: dev
          api: client
          resource_class: xlarge
      # DEPLOY prod - eu-west-1
      # feat-tournaments
      - deploy-docker:
          name: metawin-us-deploy-prod-tournament-engine-bingo-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "containers/tournament/engine/bingo"
      - deploy-docker:
          name: metawin-us-deploy-prod-tournament-engine-crash-eu-west-1
          requires:
            - metawin-us-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "containers/tournament/engine/crash"
      - deploy-docker:
          name: metawin-us-deploy-prod-tournament-engine-hilo-eu-west-1
          requires:
            - metawin-us-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "containers/tournament/engine/hilo"
      - deploy-docker:
          name: metawin-us-deploy-prod-tournament-engine-slot-eu-west-1
          requires:
            - metawin-us-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "containers/tournament/engine/slot"
      - deploy:
          name: metawin-us-deploy-prod-service-client-tournament-eu-west-1
          requires:
            - metawin-us-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/tournament"
      - deploy:
          name: metawin-us-deploy-prod-service-management-tournament-eu-west-1
          requires:
            - metawin-us-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/tournament"
      - deploy:
          name: metawin-us-deploy-prod-system-tournament-eu-west-1
          requires:
            - metawin-us-deploy-prod-tournament-engine-bingo-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/tournament"
      # integr-websockets
      - deploy-docker:
          name: metawin-us-deploy-prod-websockets-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "containers/websockets"
      # feat-activity
      - deploy:
          name: metawin-us-deploy-prod-service-client-activity-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/activity"
      # feat-amoe
      - deploy:
          name: metawin-us-deploy-prod-service-client-amoe-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/amoe"
      - deploy:
          name: metawin-us-deploy-prod-service-management-amoe-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-amoe-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/amoe"
      - deploy:
          name: metawin-us-deploy-prod-system-amoe-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-amoe-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/amoe"
      # feat-auth
      - deploy:
          name: metawin-us-deploy-prod-service-client-auth-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/auth"
      - deploy:
          name: metawin-us-deploy-prod-service-management-auth-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-auth-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/auth"
      - deploy:
          name: metawin-us-deploy-prod-system-auth-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-auth-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/auth"
          resource_class: large
      # feat-banking
      - deploy:
          name: metawin-us-deploy-prod-service-client-banking-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/banking"
      - deploy:
          name: metawin-us-deploy-prod-service-management-banking-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-banking-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/banking"
      - deploy:
          name: metawin-us-deploy-prod-system-banking-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-banking-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/banking"
      # feat-blockchain
      - deploy:
          name: metawin-us-deploy-prod-service-client-blockchain-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/blockchain"
      - deploy:
          name: metawin-us-deploy-prod-service-management-blockchain-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-blockchain-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/blockchain"
      - deploy:
          name: metawin-us-deploy-prod-system-blockchain-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-blockchain-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/blockchain"
      # feat-bonus
      - deploy:
          name: metawin-us-deploy-prod-service-client-bonus-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/bonus"
      - deploy:
          name: metawin-us-deploy-prod-service-management-bonus-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-bonus-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/bonus"
      - deploy:
          name: metawin-us-deploy-prod-system-bonus-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-bonus-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/bonus"
      # feat-chat
      - deploy:
          name: metawin-us-deploy-prod-service-client-chat-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/chat"
      - deploy:
          name: metawin-us-deploy-prod-system-chat-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-chat-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/chat"
      # feat-contact
      - deploy:
          name: metawin-us-deploy-prod-service-client-contact-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/contact"
      # feat-coupon
      - deploy:
          name: metawin-us-deploy-prod-service-client-coupon-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/coupon"
      # feat-deposits
      - deploy:
          name: metawin-us-deploy-prod-service-client-deposit-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/deposit"
      # feat-games
      - deploy:
          name: metawin-us-deploy-prod-service-client-game-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/game"
      - deploy:
          name: metawin-us-deploy-prod-service-management-game-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-game-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/game"
      - deploy:
          name: metawin-us-deploy-prod-system-game-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-game-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/game"
      # feat-hamburgers
      - deploy:
          name: metawin-us-deploy-prod-service-client-hamburger-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/hamburger"
      - deploy:
          name: metawin-us-deploy-prod-service-management-hamburger-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-hamburger-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/hamburger"
      # feat-inventory
      - deploy:
          name: metawin-us-deploy-prod-service-client-inventory-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/inventory"
      - deploy:
          name: metawin-us-deploy-prod-service-management-inventory-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-inventory-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/inventory"
      - deploy:
          name: metawin-us-deploy-prod-system-inventory-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-inventory-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/inventory"
      # feat-jackpot
      - deploy:
          name: metawin-us-deploy-prod-service-client-jackpot-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/jackpot"
      - deploy:
          name: metawin-us-deploy-prod-system-jackpot-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-jackpot-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/jackpot"
      # feat-leaderboards
      - deploy:
          name: metawin-us-deploy-prod-service-client-leaderboard-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/leaderboard"
      - deploy:
          name: metawin-us-deploy-prod-service-management-leaderboard-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-leaderboard-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/leaderboard"
      - deploy:
          name: metawin-us-deploy-prod-system-leaderboard-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-leaderboard-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/leaderboard"
      # feat-location
      - deploy:
          name: metawin-us-deploy-prod-service-client-location-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/location"
      # feat-loyalty
      - deploy:
          name: metawin-us-deploy-prod-service-client-loyalty-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/loyalty"
      - deploy:
          name: metawin-us-deploy-prod-service-management-loyalty-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-loyalty-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/loyalty"
      - deploy:
          name: metawin-us-deploy-prod-system-loyalty-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-loyalty-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/loyalty"
      # feat-nfts
      - deploy:
          name: metawin-us-deploy-prod-service-client-nft-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/nft"
      # feat-notifications
      - deploy:
          name: metawin-us-deploy-prod-service-client-notification-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/notification"
      - deploy:
          name: metawin-us-deploy-prod-system-notification-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-notification-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/notification"
      # feat-onboarding
      - deploy:
          name: metawin-us-deploy-prod-service-client-onboarding-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/onboarding"
      - deploy:
          name: metawin-us-deploy-prod-service-management-onboarding-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-onboarding-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/onboarding"
      # feat-order
      - deploy:
          name: metawin-us-deploy-prod-service-client-order-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/order"
      - deploy:
          name: metawin-us-deploy-prod-service-management-order-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-order-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/order"
      - deploy:
          name: metawin-us-deploy-prod-system-order-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-order-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/order"
      # feat-payments
      - deploy:
          name: metawin-us-deploy-prod-service-client-payment-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/payment"
      - deploy:
          name: metawin-us-deploy-prod-service-management-payment-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-payment-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/payment"
      # feat-redeemable
      - deploy:
          name: metawin-us-deploy-prod-service-client-redeemable-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/redeemable"
      - deploy:
          name: metawin-us-deploy-prod-service-management-redeemable-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-redeemable-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/redeemable"
      - deploy:
          name: metawin-us-deploy-prod-system-redeemable-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-redeemable-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/redeemable"
      # feat-referrals
      - deploy:
          name: metawin-us-deploy-prod-service-client-referral-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/referral"
      - deploy:
          name: metawin-us-deploy-prod-service-management-referral-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-referral-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/referral"
      - deploy:
          name: metawin-us-deploy-prod-system-referral-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-referral-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/referral"
      # feat-reward
      - deploy:
          name: metawin-us-deploy-prod-service-client-reward-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/reward"
      - deploy:
          name: metawin-us-deploy-prod-system-reward-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-reward-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/reward"
      # feat-social
      - deploy:
          name: metawin-us-deploy-prod-service-client-social-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/social"
      - deploy:
          name: metawin-us-deploy-prod-service-management-social-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-social-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/social"
      - deploy:
          name: metawin-us-deploy-prod-system-social-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-social-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/social"
      # feat-statistics
      - deploy:
          name: metawin-us-deploy-prod-service-client-statistics-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/statistics"
      - deploy:
          name: metawin-us-deploy-prod-service-management-statistics-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-statistics-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/statistics"
      - deploy:
          name: metawin-us-deploy-prod-system-statistics-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-statistics-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/statistics"
      # feat-store
      - deploy:
          name: metawin-us-deploy-prod-service-client-store-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/store"
      - deploy:
          name: metawin-us-deploy-prod-service-management-store-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-store-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/store"
      # feat-support
      - deploy:
          name: metawin-us-deploy-prod-service-client-support-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/support"
      # feat-sweepstakes
      - deploy:
          name: metawin-us-deploy-prod-service-client-sweepstake-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/sweepstake"
      - deploy:
          name: metawin-us-deploy-prod-service-management-sweepstake-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-sweepstake-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/sweepstake"
      - deploy:
          name: metawin-us-deploy-prod-system-sweepstake-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-sweepstake-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/sweepstake"
      # feat-upgrades
      - deploy:
          name: metawin-us-deploy-prod-service-client-upgrade-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/upgrade"
      - deploy:
          name: metawin-us-deploy-prod-service-management-upgrade-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-upgrade-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/upgrade"
      - deploy:
          name: metawin-us-deploy-prod-system-upgrade-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-upgrade-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/upgrade"
      # feat-users
      - deploy:
          name: metawin-us-deploy-prod-service-client-user-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/user"
      - deploy:
          name: metawin-us-deploy-prod-service-management-user-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-user-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/user"
      - deploy:
          name: metawin-us-deploy-prod-system-activity-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-user-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/activity"
      - deploy:
          name: metawin-us-deploy-prod-system-user-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-user-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/user"
      # feat-verification
      - deploy:
          name: metawin-us-deploy-prod-service-client-verification-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/verification"
      - deploy:
          name: metawin-us-deploy-prod-service-management-verification-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-verification-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/verification"
      - deploy:
          name: metawin-us-deploy-prod-system-verification-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-verification-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/verification"
      # feat-withdrawals
      - deploy:
          name: metawin-us-deploy-prod-service-client-withdrawal-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/client/withdrawal"
      - deploy:
          name: metawin-us-deploy-prod-service-management-withdrawal-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-client-withdrawal-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/withdrawal"
      # integr-alchemy
      - deploy:
          name: metawin-us-deploy-prod-service-integration-alchemy-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/alchemy"
      # integr-big-wheel
      - deploy:
          name: metawin-us-deploy-prod-service-integration-arena-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/arena"
      # integr-blocknative
      - deploy:
          name: metawin-us-deploy-prod-service-integration-blocknative-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/blocknative"
      # integr-bloomreach
      - deploy:
          name: metawin-us-deploy-prod-service-integration-bloomreach-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/bloomreach"
      # integr-crash
      - deploy:
          name: metawin-us-deploy-prod-service-integration-crash-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/crash"
      # integr-discord
      - deploy:
          name: metawin-us-deploy-prod-service-integration-discord-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/discord"
      # integr-evolution
      - deploy:
          name: metawin-us-deploy-prod-service-integration-evolution-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/evolution"
      # integr-fireblocks
      - deploy:
          name: metawin-us-deploy-prod-service-integration-fireblocks-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/fireblocks"
      # integr-gametech
      - deploy:
          name: metawin-us-deploy-prod-service-integration-gametech-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/gametech"
      # integr-helius
      - deploy:
          name: metawin-us-deploy-prod-service-integration-helius-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/helius"
      # integr-hub88
      - deploy:
          name: metawin-us-deploy-prod-service-integration-hub88-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/hub88"
      # integr-influencer
      - deploy:
          name: metawin-us-deploy-prod-service-integration-influencer-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/influencer"
      # integr-pragmaticplay
      - deploy:
          name: metawin-us-deploy-prod-service-integration-pragmaticplay-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/pragmaticplay"
      # integr-rubyplay
      - deploy:
          name: metawin-us-deploy-prod-service-integration-rubyplay-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/rubyplay"
      # integr-skywind
      - deploy:
          name: metawin-us-deploy-prod-service-integration-skywind-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/skywind"
      # integr-softswiss
      - deploy:
          name: metawin-us-deploy-prod-service-integration-softswiss-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/softswiss"
      # integr-tada
      - deploy:
          name: metawin-us-deploy-prod-service-integration-tada-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/tada"
      # integr-tequity
      - deploy:
          name: metawin-us-deploy-prod-service-integration-tequity-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/tequity"
      # integr-veriff
      - deploy:
          name: metawin-us-deploy-prod-service-integration-veriff-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/integration/veriff"
      # feat-admin
      - deploy:
          name: metawin-us-deploy-prod-service-management-admin-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/admin"
      - deploy:
          name: metawin-us-deploy-prod-system-admin-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-management-admin-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/admin"
      # feat-coupons
      - deploy:
          name: metawin-us-deploy-prod-service-management-coupon-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/coupon"
      # feat-deposit
      - deploy:
          name: metawin-us-deploy-prod-service-management-deposit-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/deposit"
      - deploy:
          name: metawin-us-deploy-prod-system-deposit-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-management-deposit-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/deposit"
      # feat-integrations
      - deploy:
          name: metawin-us-deploy-prod-service-management-integration-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/integration"
      # feat-jackpots
      - deploy:
          name: metawin-us-deploy-prod-service-management-jackpot-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/jackpot"
      # feat-nft
      - deploy:
          name: metawin-us-deploy-prod-service-management-nft-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/nft"
      - deploy:
          name: metawin-us-deploy-prod-system-nft-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-management-nft-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/nft"
      # feat-operations
      - deploy:
          name: metawin-us-deploy-prod-service-management-operations-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/operations"
      - deploy:
          name: metawin-us-deploy-prod-system-operations-eu-west-1
          requires:
            - metawin-us-deploy-prod-service-management-operations-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/operations"
          resource_class: large
      # feat-rewards
      - deploy:
          name: metawin-us-deploy-prod-service-management-reward-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/reward"
      # feat-sports
      - deploy:
          name: metawin-us-deploy-prod-service-management-sport-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/sport"
      # feat-sync
      - deploy:
          name: metawin-us-deploy-prod-service-management-sync-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "services/management/sync"
      # feat-alchemy
      - deploy:
          name: metawin-us-deploy-prod-system-alchemy-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/alchemy"
      # feat-blocknative
      - deploy:
          name: metawin-us-deploy-prod-system-blocknative-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/blocknative"
      # feat-crm
      - deploy:
          name: metawin-us-deploy-prod-system-crm-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/crm"
      # feat-hamburger
      - deploy:
          name: metawin-us-deploy-prod-system-hamburger-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/hamburger"
      # feat-payment
      - deploy:
          name: metawin-us-deploy-prod-system-payment-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/payment"
      # feat-slack
      - deploy:
          name: metawin-us-deploy-prod-system-slack-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/slack"
      # feat-twitter
      - deploy:
          name: metawin-us-deploy-prod-system-twitter-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/twitter"
      # feat-utilities
      - deploy:
          name: metawin-us-deploy-prod-system-utility-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/utility"
      # feat-websockets
      - deploy:
          name: metawin-us-deploy-prod-system-websockets-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/websockets"
      # feat-withdrawal
      - deploy:
          name: metawin-us-deploy-prod-system-withdrawal-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "system/withdrawal"
      # feat-testing
      - deploy-docker:
          name: metawin-us-deploy-prod-testing-bots-eu-west-1
          requires:
            - build-deploy
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "testing/bots"
      - deploy:
          name: metawin-us-deploy-prod-testing-handlers-eu-west-1
          requires:
            - metawin-us-deploy-prod-testing-bots-eu-west-1
          filters:
            branches:
              only: release/metawin-us/prod
          context: platform-metawin-us-prod
          region: eu-west-1
          brand: metawin-us
          stage: prod
          location: "testing/handlers"
