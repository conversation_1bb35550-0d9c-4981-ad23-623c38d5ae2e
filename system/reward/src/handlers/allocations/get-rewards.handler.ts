import { <PERSON>ton, Inject, IocContainer } from '@tcom/platform/lib/core/ioc';
import Logger, { LogClass } from '@tcom/platform/lib/core/logging';
import { JsonSerialiser, lambdaHandler } from '@tcom/platform/lib/core';
import { RewardBalance, RewardBalanceManager, RewardBalanceType } from '@tcom/platform/lib/reward';
import _ from 'lodash';

const BATCH_SIZE = 50;

interface Payload {
    type: RewardBalanceType;
    rewards: RewardBalance[] | string[];
    rewardCount: number;
}

@Singleton
@LogClass()
class GetRewardsHandler {
    constructor(
        @Inject private readonly serialiser: JsonSerialiser,
        @Inject private readonly manager: RewardBalanceManager) {
    }

    public async execute(payload: Payload): Promise<Payload> {
        Logger.info('Getting rewards', payload);
        const rewards = await this.manager.getNextBatch(payload.type, BATCH_SIZE);
        Logger.info('Rewards', rewards);

        return {
            ...payload,
            rewards: _.map(rewards, i => this.serialiser.serialise(i)),
            rewardCount: rewards.length
        };
    }
}

export const getRewards = lambdaHandler((payload: Payload) => IocContainer.get(GetRewardsHandler).execute(payload));