import { <PERSON>ton, Inject, IocContainer } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { lambda<PERSON>andler } from '@tcom/platform/lib/core';
import { RewardBalance, RewardBalanceManager, RewardBalanceStatus } from '@tcom/platform/lib/reward';
import { StepFunctionPayloadParser } from '@tcom/platform/lib/reward/balances/utilities';
import { RewardBalanceCache } from '@tcom/platform/lib/reward/cache';
import { RewardBalanceProcessorFactory } from '../../allocations';
import _ from 'lodash';

@Singleton
@LogClass()
class ProcessRewardHandler {
    constructor(
        @Inject private readonly balanceProcessorFactory: RewardBalanceProcessorFactory,
        @Inject private readonly parser: StepFunctionPayloadParser,
        @Inject private readonly manager: RewardBalanceManager,
        @Inject private readonly balanceCache: RewardBalanceCache) {
    }

    public async execute(payload: RewardBalance): Promise<void> {
        const rewardBalance = this.parser.parse(payload);

        if (rewardBalance.status !== RewardBalanceStatus.InProgress)
            return;

        if (!rewardBalance.eligible) {
            await this.manager.setClosed(rewardBalance.id, rewardBalance.userId, rewardBalance.type);
            return;
        }

        await this.balanceCache.lockByType(rewardBalance.userId, rewardBalance.type, async () => {
            const processor = this.balanceProcessorFactory.create(rewardBalance.type);
            await processor.process(rewardBalance);
        });
    }
}

export const processReward = lambdaHandler((payload: RewardBalance) => IocContainer.get(ProcessRewardHandler).execute(payload));