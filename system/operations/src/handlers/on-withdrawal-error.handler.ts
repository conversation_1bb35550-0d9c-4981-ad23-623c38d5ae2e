import { <PERSON><PERSON>, IocC<PERSON>r, Inject } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { Config, Environments, NotFoundError, lambdaHandler } from '@tcom/platform/lib/core';
import { PlatformEventHandler } from '@tcom/platform/lib/core/events';
import { EmailSender } from '@tcom/platform/lib/operations/email-sender';
import { WithdrawalErrorEvent } from '@tcom/platform/lib/withdrawal/events';
import { Withdrawal, WithdrawalManager } from '@tcom/platform/lib/withdrawal';
import { EmailTemplate } from '@tcom/platform/lib/operations/email-template';
import { User, UserManager } from '@tcom/platform/lib/user';
import { SNSEvent } from 'aws-lambda';
import _ from 'lodash';

@Singleton
@LogClass()
export class OnWithdrawalErrorHandler extends PlatformEventHandler<WithdrawalErrorEvent> {
    constructor(
        @Inject private readonly emailSender: EmailSender,
        @Inject private readonly withdrawalManager: WithdrawalManager,
        @Inject private readonly userManager: UserManager) {
        super();
    }

    protected async process(event: Readonly<WithdrawalErrorEvent>): Promise<void> {
        const { withdrawalId: id, reason } = event;

        const withdrawal = await this.withdrawalManager.get(id);
        if (!withdrawal)
            throw new NotFoundError(`Withdrawal ${id} not found while handling operations withdrawal error event.`);

        const user = await this.userManager.get(withdrawal.userId);

        if (!user)
            throw new NotFoundError(`User ${withdrawal.userId} not found while handling operations withdrawal error event.`);

        await this.emailSender.send(
            EmailTemplate.WithdrawalError,
            this.mapToTemplateData(withdrawal, user, reason) as any
        );
    }

    private mapToTemplateData(withdrawal: Withdrawal, user: User, reason?: string): {
         env: { stage: Environments; };
         withdrawal: {
            id: number;
            provider: string;
            userId: number;
            displayName: string | 'Anonymous';
            amount: string;
            currencyCode: string;
            network: string;
            targetCompletionTime: Date | '--';
            origin: string;
            fundsContext: string;
        },
        reason?: string,
        adminLink: string;
    } {
        return {
            withdrawal: {
                id: withdrawal.id,
                provider: withdrawal.provider,
                userId: withdrawal.userId,
                displayName: user.displayName || 'Anonymous',
                amount: withdrawal.amount.formatCurrency(withdrawal.currencyCode),
                currencyCode: withdrawal.currencyCode,
                network: withdrawal.providerNetwork || 'N/A',
                targetCompletionTime: withdrawal.targetCompletionTime || '--',
                origin: withdrawal.origin,
                fundsContext: withdrawal.fundsContext
            },
            reason,
            adminLink: `https://admin.${Config.adminDomain}/withdrawals?status=Error`,
            env: {
                stage: Config.stage
            },
        };
    }
}

export const onWithdrawalError = lambdaHandler((event: SNSEvent) => IocContainer.get(OnWithdrawalErrorHandler).execute(event));