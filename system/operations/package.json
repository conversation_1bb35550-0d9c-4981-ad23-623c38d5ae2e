{"name": "@tcom/system-operations", "@feature": "operations", "version": "1.0.0", "license": "UNLICENSED", "private": true, "author": "App Tech Development Ltd", "ci-resource-class": "large", "scripts": {"build": "tsc -b", "clean": "rm -rf .build & rm -rf node_modules & rm -rf .serverless & rm -f package-lock.json", "deploy": "slsx deploy", "remove": "slsx remove", "package": "slsx package", "deploy:templates": "slsx ses-template deploy"}, "dependencies": {"@haftahave/serverless-ses-template": "^3.0.2", "@tcom/platform": "^1.0.0", "aws-lambda": "^1.0.5", "pushover-js": "^1.3.1"}, "devDependencies": {"@tools/slsx": "^1.0.0", "@types/aws-lambda": "8.10.89", "@types/node": "^12.7.4", "serverless": "^3.39.0", "serverless-plugin-aws-exponential-backoff": "^1.0.0", "serverless-plugin-enabled": "^1.0.0", "serverless-plugin-scripts": "^1.0.2", "serverless-plugin-warmup": "8.1.0", "serverless-webpack": "^5.11.0", "ts-loader": "8.4.0", "typescript": "4.8.4", "webpack": "5.74.0", "webpack-filter-warnings-plugin": "1.2.1"}}