{"name": "@tcom/system-auth", "@feature": "auth", "version": "1.0.0", "license": "UNLICENSED", "private": true, "author": "App Tech Development Ltd", "ci-resource-class": "large", "scripts": {"build": "tsc -b", "clean": "rm -rf .build & rm -rf node_modules & rm -rf .serverless & rm -f package-lock.json", "deploy": "slsx deploy", "remove": "slsx remove", "package": "slsx package"}, "dependencies": {"@aws-crypto/client-node": "^3.1.0", "@google-cloud/recaptcha-enterprise": "^2.3.0", "@tcom/platform": "^1.0.0", "aws-lambda": "^1.0.5", "aws-sdk": "2.1230.0", "base64-js": "^1.5.1", "bs58": "^5.0.0", "env-var": "^5.0.0", "ethereumjs-util": "^7.1.3", "handlebars": "4.7.3", "libphonenumber-js": "^1.9.37", "request": "^2.88.0", "request-promise": "^4.2.5", "tweetnacl-util": "^0.15.1", "uuid": "^9.0.0", "web3": "^1.6.1"}, "devDependencies": {"@tools/slsx": "^1.0.0", "@types/aws-lambda": "8.10.89", "@types/bs58": "^4.0.4", "@types/node": "^12.7.4", "serverless": "^3.39.0", "serverless-plugin-aws-exponential-backoff": "^1.0.0", "serverless-plugin-enabled": "^1.0.0", "serverless-plugin-ifelse": "^1.0.7", "serverless-plugin-scripts": "^1.0.2", "serverless-plugin-warmup": "8.1.0", "serverless-webpack": "^5.11.0", "ts-loader": "8.4.0", "typescript": "4.8.4", "webpack": "5.74.0", "webpack-filter-warnings-plugin": "1.2.1"}}