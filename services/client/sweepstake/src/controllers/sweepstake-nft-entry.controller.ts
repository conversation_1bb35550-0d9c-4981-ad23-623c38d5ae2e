import Logger, { LogClass } from '@tcom/platform/lib/core/logging';
import { ClientController, Path, Security, Throttle } from '@tcom/platform/lib/api';
import { Inject } from '@tcom/platform/lib/core/ioc';
import { Sweepstake, SweepstakeEntrySetManager, SweepstakeManager, SweepstakeType } from '@tcom/platform/lib/sweepstake';
import { SweepstakeEntryMediatorFactory } from '@tcom/platform/lib/sweepstake/mediators';
import { Get, Post, Route, Tags } from '@tsoa/runtime';
import { SweepstakeUserEnteredModel } from '@tcom/platform/lib/sweepstake/models';
import { getPrimaryAlias, isAnonOrStandard } from '@tcom/platform/lib/user/utilities';
import { ForbiddenError, JsonSerialiser, NotFoundError, Redis } from '@tcom/platform/lib/core';
import { locationCheckRequired } from '@tcom/platform/lib/auth/utilities';
import { isCountryBlocked } from '@tcom/platform/lib/sweepstake/utilities';
import { SweepstakeUserEnteredModelMapper } from '@tcom/platform/lib/sweepstake/models/mappers';
import { UserAliasType, UserType } from '@tcom/platform/lib/user';
import { CacheKeyGenerator } from '@tcom/platform/lib/core/cache';
import { BlockchainNetwork } from '@tcom/platform/lib/blockchain';
import { SweepstakeEligibilityProcessor } from '@tcom/platform/lib/sweepstake/eligibility';
import moment from 'moment';
import _ from 'lodash';
import { SweepstakeEntrySetCache, SweepstakeNFTEntryCache, UsedTokenData } from '@tcom/platform/lib/sweepstake/cache';
import { SweepstakeRefreshActivityEvent } from '@tcom/platform/lib/sweepstake/events';
import { PlatformEventDispatcher } from '@tcom/platform/lib/core/events';
import { NFT, NFTManager } from '@tcom/platform/lib/nft';

interface SweepstakeNFTEntryAvailability {
    nftCount: number;
    entryCount: number;
    unusedNFTs?: EntryNFT[];
}

interface EntryNFT extends NFT {
    entryCount: number;
}

interface NFTResults {
    unused: NFT[];
    used?: UsedTokenData[];
    totalCount: number;
}

interface OwnedNFTs {
    nfts: NFT[];
    totalCount: number;
}

interface TraitMultiplierConfig {
    [trait: string]: number;
}

interface TraitMultiplierConfigRoot {
    [trait: string]: TraitMultiplierConfig | number;
}

@Tags('Sweepstakes')
@Route('sweepstake/nft/entry')
@LogClass()
export class SweepstakeNFTEntryController extends ClientController {
    private readonly cacheKeyGenerator = new CacheKeyGenerator('SWEEPSTAKE-NFT-ENTRY');

    constructor(
        @Inject private readonly sweepstakeManager: SweepstakeManager,
        @Inject private readonly sweepstakeEntrySetManager: SweepstakeEntrySetManager,
        @Inject private readonly sweepstakeEntryMediatorFactory: SweepstakeEntryMediatorFactory,
        @Inject private readonly sweepstakeUserEnteredModelMapper: SweepstakeUserEnteredModelMapper,
        @Inject private readonly sweepstakeEligibilityProcessor: SweepstakeEligibilityProcessor,
        @Inject private readonly serialiser: JsonSerialiser,
        @Inject private readonly redis: Redis,
        @Inject private readonly cache: SweepstakeNFTEntryCache,
        @Inject private readonly entrySetCache: SweepstakeEntrySetCache,
        @Inject private readonly eventDispatcher: PlatformEventDispatcher,
        @Inject private readonly nftManager: NFTManager) {
        super();
    }

    /**
     * @summary Check if authenticated user can enter active sweepstake using NFTs
     * @param id Sweepstake ID
     * @isInt id
     */
    @Get('{id}/available')
    @Security('cognito')
    @Throttle()
    public async available(@Path() id: number): Promise<SweepstakeNFTEntryAvailability> {
        const sweepstake = await this.sweepstakeManager.getActive(id);

        const [ethereumAddress, nftContractAddress] = this.validate(sweepstake);

        const nftNetwork = sweepstake.metadata?.freeEntryNFTNetwork || BlockchainNetwork.Ethereum;

        const { unused, used, totalCount } = await this.getUnusedNFTs(sweepstake, ethereumAddress, nftContractAddress, nftNetwork);
        Logger.info(`Found ${unused.length} NFTs for sweepstake ${id}.`, unused);
        const entrycountUnused: EntryNFT[] = unused.map(n => ({
            ...n,
            entryCount: this.getNFTEntryCount(sweepstake, n)
        }));

        const entryCount = _.sumBy(entrycountUnused, u => u.entryCount);

        if (used?.length) {
            const usedByOtherOwners = used.filter(u => u.userId !== this.user.id);
            Logger.info('Found used NFTs from other users data', usedByOtherOwners);
        }

        return {
            nftCount: totalCount,
            entryCount,
            unusedNFTs: entrycountUnused.length ? entrycountUnused : undefined
        };
    }

    @Get('{id}/token/{tokenId}/used')
    @Security('cognito')
    @Throttle()
    public async usedTokens(@Path() id: number, @Path() tokenId: string): Promise<boolean> {
        const sweepstake = await this.sweepstakeManager.getActive(id);

        const contractAddress = sweepstake.metadata?.freeEntryNFTContractAddress;

        if (!contractAddress)
            throw new ForbiddenError(`Sweepstake ${sweepstake.id} not supported.`);

        const data = await this.cache.getUsed(sweepstake.id, tokenId);

        if (data?.userId !== this.user.id)
            return false;

        return true;
    }

    /**
     * @summary Enter authenticated user into active sweepstake using NFTs
     * @param id Sweepstake ID
     * @isInt id
     */
    @Post('{id}')
    @Security('cognito')
    @Throttle()
    public async enter(@Path() id: number): Promise<SweepstakeUserEnteredModel> {
        return this.redis.lock(`SWEEPSTAKE-NFT-ENTRY:${this.user.id}`, async () => {
            const sweepstake = await this.sweepstakeManager.getActive(id);

            const [ethereumAddress, nftContractAddress] = this.validate(sweepstake);

            const nftNetwork = sweepstake.metadata?.freeEntryNFTNetwork || BlockchainNetwork.Ethereum;
            const entriesPerNFT = sweepstake.metadata?.freeEntryNFTEntriesPerNFT || 1;

            const { unused, used } = await this.getUnusedNFTs(sweepstake, ethereumAddress, nftContractAddress, nftNetwork);
            const entryCount = _.sumBy(unused, u => this.getNFTEntryCount(sweepstake, u));

            if (entryCount === 0)
                throw new ForbiddenError(`No NFTs available for sweepstake ${id}.`);

            const result = await this.sweepstakeEligibilityProcessor.process(sweepstake, this.user.id);

            if (!result.passed)
                throw new ForbiddenError(result.message);

            const mediator = this.sweepstakeEntryMediatorFactory.create(sweepstake.type);

            if (used?.length) {
                const usedByOtherOwners = used.filter(u => u.userId !== this.user.id);
                Logger.info('Found used NFTs from other users data', usedByOtherOwners);

                const groupedByEntrySet = _.groupBy(usedByOtherOwners, 'entrySetId');

                for (const entrySetId of Object.keys(groupedByEntrySet)) {
                    const data = groupedByEntrySet[entrySetId];
                    const totalDeduction = _.sumBy(data, d => d.entryCount || entriesPerNFT);

                    await this.entrySetCache.lock(sweepstake.id, data[0].userId, async () => {
                        const usedEntrySet = await this.sweepstakeEntrySetManager.getById(Number(entrySetId));

                        if (!usedEntrySet)
                            return;

                        const newCount = Math.max(usedEntrySet.count - totalDeduction, 0);

                        if (newCount === 0)
                            await this.sweepstakeEntrySetManager.removeById(Number(entrySetId));
                        else
                            await this.sweepstakeEntrySetManager.updateCount(Number(entrySetId), newCount);

                        await this.eventDispatcher.send(new SweepstakeRefreshActivityEvent(sweepstake.id));
                    });
                }

                await this.cache.removeUsed(sweepstake.id, usedByOtherOwners.map(u => u.tokenId));
            }

            const entrySet = await mediator.add(sweepstake, this.user, {
                skipPayment: true,
                entryCount,
                source: 'NFT'
            });

            const usedTokenData = unused.map(n => ({
                contractAddress: nftContractAddress,
                tokenId: n.tokenId,
                userId: this.user.id,
                entrySetId: entrySet.id,
                owner: ethereumAddress,
                entryCount: this.getNFTEntryCount(sweepstake, n)
            }));

            await this.storeUsedTokens(sweepstake, usedTokenData);

            return this.sweepstakeUserEnteredModelMapper.from(id, this.user.id, entrySet);
        }, 10000);
    }

    private async getUnusedNFTs(sweepstake: Sweepstake, address: string, contractAddress: string, network: BlockchainNetwork): Promise<NFTResults> {
        const { nfts, totalCount } = await this.getNFTsForOwner(address, contractAddress, network);

        const usedTokens = await this.cache.getManyUsed(sweepstake.id, nfts.map(n => n.tokenId));
        let unusedNFTs = nfts.filter(n => !usedTokens.some(t => t.tokenId === n.tokenId && t.userId === this.user.id));

        const traitFilter = sweepstake.metadata?.freeEntryNFTTraitFilter;

        if (traitFilter) {
            const traitFilterKeys = Object.keys(traitFilter);

            unusedNFTs = unusedNFTs.filter(n => {
                for (const trait of traitFilterKeys) {
                    const traitValue = n.traits.find(t => t.type === trait)?.value;

                    if (!traitValue)
                        return false;

                    const traitTargetValue = traitFilter[trait];

                    if (Array.isArray(traitTargetValue) && traitTargetValue.includes(traitValue))
                        return true;

                    if (traitTargetValue === traitValue)
                        return true;

                    if (traitTargetValue === 'Any')
                        return true;
                }

                return false;
            });
        }

        return { unused: unusedNFTs, totalCount, used: usedTokens };
    }

    private async storeUsedTokens(sweepstake: Sweepstake, data: UsedTokenData[]): Promise<void> {
        const expiry = sweepstake.drawTime ? moment(sweepstake.drawTime).add(3, 'days') : moment().add(90, 'days');
        await this.cache.storeUsed(sweepstake.id, data, expiry.toDate());
    }

    private async getNFTsForOwner(address: string, contractAddress: string, network: BlockchainNetwork): Promise<OwnedNFTs> {
        const cacheKey = this.cacheKeyGenerator.generate(`NFTs:${address}`);

        const cached = await this.redis.cluster.get(cacheKey);

        if (cached)
            return this.serialiser.deserialise<OwnedNFTs>(cached);

        const all = await this.nftManager.getAll({
            address: contractAddress,
            network,
            owners: [address]
        });

        const result: OwnedNFTs = {
            nfts: all.items,
            totalCount: all.totalCount
        };

        await this.redis.cluster.setex(cacheKey, 30, this.serialiser.serialise(result));
        return result;
    }

    private validate(sweepstake: Sweepstake): [string, string] {
        const ethereumAddress = getPrimaryAlias(this.user, UserAliasType.EthereumAddress);

        if (!ethereumAddress)
            throw new ForbiddenError('User does not have an Ethereum address.');

        if (isAnonOrStandard(this.user) && !sweepstake.public)
            throw new NotFoundError(`Sweepstake ${sweepstake.id} not found.`);

        if (sweepstake.metadata?.inviteOnly)
            throw new ForbiddenError(`Sweepstake ${sweepstake.id} is invite only.`);

        if (this.user.type === UserType.Internal && sweepstake.public)
            throw new ForbiddenError(`Internal users cannot enter public sweepstakes.`);

        if (locationCheckRequired(this.user) && isCountryBlocked(sweepstake, this.request.geoIp))
            throw new ForbiddenError('Sorry, this competition is not available in your location.');

        if (sweepstake.type === SweepstakeType.Blockchain)
            throw new ForbiddenError(`Sweepstake type '${sweepstake.type}' not supported.`);

        const nftContractAddress: string = sweepstake.metadata?.freeEntryNFTContractAddress;

        if (!nftContractAddress)
            throw new ForbiddenError(`Sweepstake ${sweepstake.id} not supported.`);

        return [ethereumAddress, nftContractAddress];
    }

    private getNFTEntryCount(sweepstake: Sweepstake, nft: NFT): number {
        const entriesPerNFT = sweepstake.metadata?.freeEntryNFTEntriesPerNFT || 1;
        const nftTraitMultiplierConfig: TraitMultiplierConfigRoot | undefined = sweepstake.metadata?.freeEntryNFTTraitMultiplierConfig;

        if (!nftTraitMultiplierConfig)
            return entriesPerNFT;

        let multiplier = 1;

        let highestMultiplier = 1;

        for (const trait of Object.keys(nftTraitMultiplierConfig)) {
            const traitValue = nft.traits.find(t => t.type === trait)?.value;

            if (!traitValue || traitValue === 'None')
                continue;

            const traitMultiplier = nftTraitMultiplierConfig[trait];

            let currentMultiplier = 1;

            if (typeof traitMultiplier === 'number')
                currentMultiplier = traitMultiplier;
            else
                currentMultiplier = traitMultiplier[traitValue];

            if (currentMultiplier > highestMultiplier)
                highestMultiplier = currentMultiplier;
        }

        multiplier = highestMultiplier;

        return multiplier * entriesPerNFT;
    }
}