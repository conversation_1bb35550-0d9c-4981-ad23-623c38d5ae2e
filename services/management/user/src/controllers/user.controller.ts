import { AdminController, Get, Route, Tags, Body, Put, SuccessResponse, Path, Security, Query, Response, Post, Delete } from '@tcom/platform/lib/api';
import { Inject } from '@tcom/platform/lib/core/ioc';
import { BadRequestError, NotFoundError, PagedResult, RequesterType } from '@tcom/platform/lib/core';
import {
    UserManager,
    User,
    UserProfile,
    UserProfileUpdate,
    UserType,
    UserIp,
    DisplayNameValidationResult,
    UserNotificationType,
    UserNotificationSettingManager,
    UserNotificationSetting,
    UserNotificationChannel,
    UserNotificationSettingUpdate,
    UserSanctions,
    UserTagManager,
    UserBonusStatus,
    UserAliasType,
    UserMetadata,
    UserTransactionManager,
    TransactionResults,
    UserProfileUpdateValidator,
    UserDisplayNameHistoryItem,
    UserVerificationStatus,
    UserDisplayNameHistoryFilter
} from '@tcom/platform/lib/user';
import { LogClass, LogOriginator, LogType, UserLogMessage } from '@tcom/platform/lib/core/logging';
import { LogAudit } from '@tcom/platform/lib/admin/decorators';
import { UserFilter } from '@tcom/platform/lib/user/user-filter';
import { UserLogMessageFilter } from '@tcom/platform/lib/user/user-log-message-filter';
import { UserCountryChangeModel, UserDisplayNameCheckModel, UserDisplayNameChangeModel } from '@tcom/platform/lib/user/models';
import { UserBanModel, FraudulentUserModel, UserUpdateTagsModel, UserBonusStatusChangeModel, UserLevelUpdateModel, UserVerificationStatusChangeModel } from '../models';
import { CRMSender, CRMTemplateName, CRMUserIdentifierType } from '@tcom/platform/lib/crm';
import { SocialProvider } from '@tcom/platform/lib/social';
import { splitCamelCase } from '@tcom/platform/lib/core/utilities';

@Tags('Users')
@Route('user')
@LogClass()
export class UserController extends AdminController {
    constructor(
        @Inject private readonly userManager: UserManager,
        @Inject private readonly crmSender: CRMSender,
        @Inject private readonly userNotificationSettingManager: UserNotificationSettingManager,
        @Inject private readonly userTransactionManager: UserTransactionManager,
        @Inject private readonly userTagManager: UserTagManager,
        @Inject private readonly profileUpdateValidator: UserProfileUpdateValidator) {
        super();
    }

    /**
     * @summary Gets a list of users
     */
    @Get()
    @Security('admin', ['user:read'])
    public async getAll(
        @Query() type?: UserType,
        @Query() enabled?: boolean,
        @Query() subscribed?: boolean,
        @Query() subscribing?: boolean,
        @Query() displayName?: string,
        @Query() email?: string,
        @Query() page: number = 1,
        @Query() pageSize: number = 20,
        @Query() playedFrom?: Date,
        @Query() playedTo?: Date,
        @Query() lastUpdatedFrom?: Date,
        @Query() lastUpdatedTo?: Date,
        @Query() createdFrom?: Date,
        @Query() createdTo?: Date,
        @Query() regCountry?: string,
        @Query() tag?: string[],
        @Query() bonusStatus?: UserBonusStatus,
        @Query() order?: string,
        @Query() ipAddress?: string,
        @Query() alias?: string,
        @Query() aliasType?: UserAliasType,
        @Query() deleted?: boolean,
        @Query() socialUsername?: string,
        @Query() socialProvider?: SocialProvider,
        @Query() identityStatus?: UserVerificationStatus,
        @Query() direction?: 'ASC' | 'DESC'): Promise<PagedResult<User>> {
        const filter: UserFilter = {
            type,
            enabled,
            subscribed,
            subscribing,
            page,
            pageSize,
            regCountry,
            deleted,
            fields: {
                displayName,
                email,
                playedFrom,
                playedTo,
                lastUpdatedFrom,
                lastUpdatedTo,
                createdFrom,
                createdTo,
                tag,
                bonusStatus,
                ipAddress,
                alias,
                aliasType,
                socialUsername,
                socialProvider,
                identityStatus
            },
        };

        if (order)
            filter.order = {
                [`${order}`]: direction || 'ASC'
            };

        const users = await this.userManager.getAll(filter);
        return users;
    }

    /**
     * @summary Gets all user log messages, matching the given filters
     * @isDateTime createdFrom
     * @isDateTime createdTo
     */
    @Get('user-log-message')
    @Security('admin', ['user:read'])
    @SuccessResponse(200, 'Ok')
    public async getAllUserLogs(
        @Query() userId?: number,
        @Query() type?: LogType,
        @Query() originator?: LogOriginator,
        @Query() originatorId?: string,
        @Query() application?: string,
        @Query() action?: string,
        @Query() createdFrom?: Date,
        @Query() createdTo?: Date,
        @Query() page: number = 1,
        @Query() pageSize: number = 20): Promise<PagedResult<UserLogMessage>> {
        const filter: UserLogMessageFilter = {
            userId,
            type,
            originator,
            originatorId,
            application,
            action,
            createdFrom,
            createdTo,
            page,
            pageSize,
        };

        return this.userManager.getAllUserLogMessages(filter);
    }

    /**
     * @summary Gets a user
     */
    @Get('{id}')
    @Security('admin', ['user:read'])
    public async get(@Path() id: number): Promise<User> {
        const user = await this.userManager.get(id, false, true);
        if (!user)
            throw new NotFoundError('User not found.');

        return user;
    }

    /**
     * @summary Gets a user by email address
     */
    @Get('email/{email}')
    @Security('admin', ['user:read'])
    public async getByEmail(email: string): Promise<User> {
        const profile = await this.userManager.getProfileByEmail(email);
        if (!profile)
            throw new NotFoundError('User not found.');

        const user = await this.userManager.get(profile.userId);
        if (!user)
            throw new NotFoundError('User not found.');

        return user;
    }

    /**
     * @summary Enable a user
     */
    @Put('{id}/enable')
    @LogAudit('User', 'Enable User')
    @Security('admin', ['user:write'])
    public async enable(@Path() id: number): Promise<void> {
        await this.userManager.setEnabled(id, true);
    }

    /**
     * @summary Disable a user
     */
    @Put('{id}/disable')
    @LogAudit('User', 'Disable User')
    @Security('admin', ['user:write'])
    public async disable(@Path() id: number): Promise<void> {
        await this.userManager.setEnabled(id, false);
    }

    /**
     * @summary Set user level
     */
    @Put('{id}/level')
    @LogAudit('User', 'Set User Level')
    @Security('admin', ['user:write'])
    public async setLevel(@Path() id: number, @Body() model: UserLevelUpdateModel): Promise<void> {
        await this.userManager.setLevel(id, model.level, RequesterType.Employee, this.user.id);
    }

    /**
     * @summary Ban a user with optional reason
     */
    @Put('{id}/ban')
    @LogAudit('User', 'Ban User')
    @Security('admin', ['user:write'])
    public async ban(@Path() id: number, @Body() payload: UserBanModel): Promise<void> {
        const { sendEmail, reason } = payload;

        if (sendEmail)
            await this.crmSender.send({ type: CRMUserIdentifierType.Id, id }, UserNotificationType.Account, CRMTemplateName.BanUser, { data: { Reason: reason } });

        await this.userManager.setEnabled(id, false);
    }

    /**
     * @summary Sets a user to internal type
     */
    @Put('{id}/internal')
    @LogAudit('User', 'Make User Internal')
    @Security('admin', ['user:write'])
    public async setInternal(@Path() id: number): Promise<void> {
        await this.userManager.setType(id, UserType.Internal);
    }

    /**
     * @summary Sets a user to standard type
     */
    @Put('{id}/standard')
    @LogAudit('User', 'Make User Standard')
    @Security('admin', ['user:write'])
    public async setStandard(@Path() id: number): Promise<void> {
        await this.userManager.setType(id, UserType.Standard);
    }

    /**
     * @summary Gets a users profile
     */
    @Get('{id}/profile')
    @Security('admin', ['user:read'])
    public async getProfile(@Path() id: number): Promise<UserProfile> {
        const profile = await this.userManager.getProfile(id);

        if (!profile)
            throw new NotFoundError('User not found');

        return profile;
    }

    /**
     * @summary Sets a users profile
     */
    @Put('{id}/profile')
    @LogAudit('User', 'Update User Profile')
    @Security('admin', ['user:write:profile'])
    @SuccessResponse(200, 'Ok')
    public async setProfile(@Path() id: number, @Body() profile: UserProfileUpdate): Promise<void> {
        const result = await this.profileUpdateValidator.validate(id, profile, {
            throttle: true,
            permissions: true,
            clear: true
        });

        if (!result.valid)
            throw new BadRequestError(`Cannot update profile: ${splitCamelCase(result.outcome)}`);

        await this.userManager.setProfile(id, profile);
        this.setStatus(200);
    }

    /**
     * @summary Gets a users IP history
     */
    @Get('{id}/ip/history')
    @Security('admin', ['user:read'])
    @SuccessResponse(200, 'Ok')
    public async getIpHistory(@Path() id: number): Promise<UserIp[]> {
        return this.userManager.getIpHistory(id);
    }

    /**
     * @summary Gets a users that have used the supplied IP address
     */
    @Get('ip/{ip}/user')
    @Security('admin', ['user:read'])
    @SuccessResponse(200, 'Ok')
    public async getUsersByIp(@Path() ip: string): Promise<User[]> {
        return this.userManager.getUsersByIp(ip);
    }

    /**
     * @summary Gets display name history
     */
    @Get('display-name/history')
    @Security('admin', ['user:read'])
    @SuccessResponse(200, 'Ok')
    public async getDisplayNameHistory(
        @Query() userId?: number,
        @Query() name?: string,
        @Query() fromDate?: Date,
        @Query() toDate?: Date,
        @Query() page: number = 1,
        @Query() pageSize: number = 20
    ): Promise<PagedResult<UserDisplayNameHistoryItem>> {
        const filter: UserDisplayNameHistoryFilter = {
            userId,
            name,
            fromDate,
            toDate,
            page,
            pageSize
        };

        return this.userManager.getDisplayNameHistory(filter);
    }

    /**
     * @summary Sets the users display country
     */
    @Put('{id}/country')
    @LogAudit('User', 'Update User Country')
    @Security('admin', ['user:write'])
    @Response<NotFoundError>(404, 'User not found')
    public async setCountry(@Path() id: number, @Body() change: UserCountryChangeModel): Promise<void> {
        await this.userManager.setCountry(id, change.country);
    }

    /**
     * @summary Sets the user AML status
     */
    @Put('{id}/aml-status')
    @LogAudit('User', 'Update User AML Status')
    @Security('admin', ['user:write'])
    @Response<NotFoundError>(404, 'User not found')
    public async setAMLStatus(@Path() id: number, @Body() change: UserVerificationStatusChangeModel): Promise<void> {
        await this.userManager.setAMLStatus(id, change.status);
    }

    /**
     * @summary Sets the user identity status
     */
    @Put('{id}/identity-status')
    @LogAudit('User', 'Update User Identity Status')
    @Security('admin', ['user:write'])
    @Response<NotFoundError>(404, 'User not found')
    public async setIdentityStatus(@Path() id: number, @Body() change: UserVerificationStatusChangeModel): Promise<void> {
        await this.userManager.setIdentityStatus(id, change.status);
    }

    /**
     * @summary Sets the user identity status
     */
    @Put('{id}/address-status')
    @LogAudit('User', 'Update User Identity Status')
    @Security('admin', ['user:write'])
    @Response<NotFoundError>(404, 'User not found')
    public async setAddressStatus(@Path() id: number, @Body() change: UserVerificationStatusChangeModel): Promise<void> {
        await this.userManager.setAddressStatus(id, change.status);
    }

    /**
     * @summary Sets the user display name
     */
    @Put('{id}/display-name')
    @LogAudit('User', 'Update User Display Name')
    @Security('admin', ['user:write'])
    @Response<NotFoundError>(404, 'User not found')
    public async setDisplayName(@Path() id: number, @Body() displayName: UserDisplayNameChangeModel): Promise<void> {
        await this.userManager.setDisplayName(id, displayName.name);
    }

    /**
     * @summary Validate user display name
     */

    @Post('{id}/display-name/check')
    @Security('admin', ['user:read'])
    @Response<NotFoundError>(404, 'User not found')
    public async checkDisplayName(@Path() id: number, @Body() check: UserDisplayNameCheckModel): Promise<DisplayNameValidationResult> {
        return this.userManager.validateDisplayName(check.name, id);
    }

    /**
     * @summary Returns current user notification settings
     */

    @Get('{id}/notification-setting')
    @Security('admin', ['user:read'])
    @Response<NotFoundError>(404, 'User not found')
    public async viewNotificationsSettings(@Path() id: number): Promise<UserNotificationSetting[]> {
        return this.userNotificationSettingManager.getAll(id);
    }

    /**
     * @summary Returns current user transaction history
     */
    @Get('{walletAddress}/transaction-history')
    @Security('admin', ['user:read'])
    @Response<NotFoundError>(404, 'User not found')
    public async userTransactions(@Path() walletAddress: string): Promise<TransactionResults> {
        return this.userTransactionManager.getAll(walletAddress);
    }

    /**
     * @summary Updates users notification setting by channel
     */
    @Put('{id}/notification-setting/{channel}')
    @LogAudit('User', 'Update User Notification Settings')
    @Security('admin', ['user:write'])
    public async updateNotificationsSettings(@Path() id: number, @Path() channel: UserNotificationChannel, @Body() update: UserNotificationSettingUpdate): Promise<void> {

        if (Object.keys(update).length === 0)
            throw new BadRequestError('No settings supplied.');

        await this.userNotificationSettingManager.set(id, channel, update);
    }

    @Put('{id}/fraudulent')
    @LogAudit('User', 'Set User As Fraudulent')
    @Security('admin', ['user:write'])
    public async setFraudulent(@Path() id: number, @Body() fraudulent: FraudulentUserModel): Promise<void> {
        await this.userManager.setFraudulent(id, fraudulent.isFraudulent);
    }

    @Put('{id}/sanctions')
    @LogAudit('User', 'Set User Sanctions')
    @Security('admin', ['user:write'])
    public async setSanctions(@Path() id: number, @Body() sanctions: UserSanctions): Promise<void> {
        await this.userManager.setSanctions(id, sanctions);
    }

    /**
     * @summary Deletes a user by removing their PII
     */
    @Delete('{id}')
    @LogAudit('User', 'Delete User')
    @Security('admin', ['user:delete'])
    public async remove(@Path() id: number): Promise<void> {
        const user = await this.userManager.get(id);

        if (!user)
            throw new NotFoundError('User not found');

        await this.userManager.remove(id);
    }

    /**
     * @summary Updates tags for a user
     */
    @Put('{id}/tags')
    @LogAudit('User', 'Update User Tags')
    @Security('admin', ['user:tags:write'])
    public async updateTags(@Path() id: number, @Body() update: UserUpdateTagsModel): Promise<void> {
        if (update.tags.some(t => t.startsWith('$')))
            throw new BadRequestError('Custom tags cannot be prefixed with "$".');

        await this.userTagManager.update(id, update.tags, '$');
    }

    /**
     * @summary Remove all tags from user
     */
    @Delete('{id}/tags')
    @LogAudit('User', 'Delete All User Tags')
    @Security('admin', ['user:tags:delete'])
    public async removeAllTags(@Path() id: number): Promise<void> {
        await this.userTagManager.removeAll(id, '$');
    }

    /**
     * @summary Update a user bonus status
     */
    @Put('{id}/bonus-status')
    @LogAudit('User', 'Set User Bonus Status')
    @Security('admin', ['user:write'])
    public async setBonusStatus(@Path() id: number, @Body() update: UserBonusStatusChangeModel): Promise<void> {
        await this.userManager.setBonusStatus(id, update.status);
    }

    /**
     * @summary Update a users metadata
     */
    @Put('{id}/metadata')
    @LogAudit('User', 'Set User Metadata')
    @Security('admin', ['user:write'])
    public async setMetadata(@Path() id: number, @Body() metadata: UserMetadata): Promise<void> {
        await this.userManager.setMetadata(id, metadata);
    }
}