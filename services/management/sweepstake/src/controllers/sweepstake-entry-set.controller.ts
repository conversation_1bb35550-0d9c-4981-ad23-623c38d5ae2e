import { AdminController, Get, Post, Route, Query, Tags, Path, Security, Body, Delete } from '@tcom/platform/lib/api';
import { Inject } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { LogAudit } from '@tcom/platform/lib/admin/decorators';
import { NotFoundError, PagedResult } from '@tcom/platform/lib/core';
import { PlatformEventDispatcher } from '@tcom/platform/lib/core/events';
import { SweepstakeManager, SweepstakeEntryState, SweepstakeType, SweepstakePresentationType, SweepstakeMechanism, SweepstakeEntrySet, SweepstakeEntrySetFilter, SweepstakeEntrySetManager, SweepstakeGameBuyInBalanceManager } from '@tcom/platform/lib/sweepstake';
import { SweepstakeFreeEntryBulkProcessor, SweepstakeFreeEntryBulkResult } from '@tcom/platform/lib/sweepstake';
import { SweepstakeRefreshActivityEvent } from '@tcom/platform/lib/sweepstake/events';
import { SweepstakeEntryMediatorFactory } from '@tcom/platform/lib/sweepstake/mediators';
import { AddSweepstakeEntryModel, AddSweepstakeFreeEntryModel } from '../models';

@Tags('Sweepstake Entry Sets')
@Route('sweepstake')
@LogClass()
export class SweepstakeEntrySetController extends AdminController {
    constructor(
        @Inject private readonly sweepstakeManager: SweepstakeManager,
        @Inject private readonly sweepstakeEntrySetManager: SweepstakeEntrySetManager,
        @Inject private readonly gameBuyInBalanceManager: SweepstakeGameBuyInBalanceManager,
        @Inject private readonly sweepstakeEntryMediatorFactory: SweepstakeEntryMediatorFactory,
        @Inject private readonly sweepstakeFreeEntryBulkProcessor: SweepstakeFreeEntryBulkProcessor,
        @Inject private readonly eventDispatcher: PlatformEventDispatcher) {
        super();
    }

    /**
     * @summary Gets a list of sweepstake entry sets
     */
    @Get('entry/set')
    @Security('admin', ['sweepstake:entry:read'])
    public async getAll(
        @Query() createdFrom?: Date,
        @Query() createdTo?: Date,
        @Query() sweepstakeId?: number,
        @Query() sweepstakeType?: SweepstakeType,
        @Query() sweepstakePresentationType?: SweepstakePresentationType,
        @Query() sweepstakeMechanism?: SweepstakeMechanism,
        @Query() sweepstakeName?: string,
        @Query() userId?: number,
        @Query() externalId?: string,
        @Query() states?: SweepstakeEntryState[],
        @Query() page: number = 1,
        @Query() pageSize: number = 20,
        @Query() order: string = 'createTime',
        @Query() direction: 'ASC' | 'DESC' = 'DESC'
    ): Promise<PagedResult<SweepstakeEntrySet>> {
        const filter: SweepstakeEntrySetFilter = {
            dateFrom: createdFrom,
            dateTo: createdTo,
            sweepstakeId,
            sweepstakeType,
            sweepstakePresentationType,
            sweepstakeMechanism,
            sweepstakeName,
            userId,
            externalId,
            states,
            page,
            pageSize,
            order: {
                [`${order}`]: direction
            }
        };

        return this.sweepstakeEntrySetManager.getAll(filter);
    }

    /**
     * @summary Gets a sweepstake entry set by ID
     */
    @Get('{sweepstakeId}/entry/set/{id}')
    @Security('admin', ['sweepstake:entry:read'])
    public async getById(@Path() sweepstakeId: number, @Path() id: number): Promise<SweepstakeEntrySet> {
        const sweepstake = await this.sweepstakeManager.get(sweepstakeId);

        if (!sweepstake)
            throw new NotFoundError('Sweepstake not found.');

        const entrySet = await this.sweepstakeEntrySetManager.getById(id);

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${id} not found.`);

        return entrySet;
    }

    /**
     * @summary Gets a list of sweepstake entry sets for user
     */
    @Get('{sweepstakeId}/entry/set/user/{userId}')
    @Security('admin', ['sweepstake:entry:read'])
    public async get(@Path() sweepstakeId: number, @Path() userId: number): Promise<SweepstakeEntrySet[]> {
        const sweepstake = await this.sweepstakeManager.get(sweepstakeId);

        if (!sweepstake)
            throw new NotFoundError(`Sweepstake ${sweepstakeId} not found.`);

        return this.sweepstakeEntrySetManager.get(sweepstake.id, userId);
    }

    /**
     * @summary Creates a new sweepstake entry set
     */
    @Post('{sweepstakeId}/entry/set')
    @LogAudit('Sweepstake Entry Set', 'Add Sweepstake Entry Set')
    @Security('admin', ['sweepstake:entry:write'])
    public async add(@Path() sweepstakeId: number, @Body() model: AddSweepstakeEntryModel): Promise<SweepstakeEntrySet> {
        const sweepstake = await this.sweepstakeManager.get(sweepstakeId);

        if (!sweepstake)
            throw new NotFoundError('Sweepstake not found.');

        const mediator = this.sweepstakeEntryMediatorFactory.create(sweepstake.type);
        const entrySet = await mediator.add(sweepstake, model.userId, {
            packageId: model.packageId,
            skipPayment: model.skipPayment,
            fundsContext: model.fundsContext,
            source: !model.packageId ? 'AMOE' : undefined
        });

        await this.gameBuyInBalanceManager.addEntry(entrySet);
        return entrySet;
    }

    /**
     * @summary Bulk uploads free entries to blockchain sweepstake
     */
    @Post('{sweepstakeId}/entry/set/bulk')
    @LogAudit('Sweepstake Entry Set', 'Bulk Upload Sweepstake Free Entries')
    @Security('admin', ['sweepstake:entry:write'])
    public async upload(@Path() sweepstakeId: number, @Body() model: AddSweepstakeFreeEntryModel): Promise<SweepstakeFreeEntryBulkResult> {
        return this.sweepstakeFreeEntryBulkProcessor.process(sweepstakeId, model.userAliases, model.packageId);
    }

    /**
     * @summary Voids a sweepstake entry set
     */
    @Delete('{sweepstakeId}/entry/set/{id}/void')
    @LogAudit('Sweepstake Entry Set', 'Refund Sweepstake Entry Set')
    @Security('admin', ['sweepstake:entry:void'])
    public async void(@Path() sweepstakeId: number, @Path() id: number): Promise<void> {
        const sweepstake = await this.sweepstakeManager.get(sweepstakeId);

        if (!sweepstake)
            throw new NotFoundError('Sweepstake not found.');

        await this.sweepstakeEntrySetManager.void(id);
        await this.gameBuyInBalanceManager.voidEntry(id);

        await this.eventDispatcher.send(new SweepstakeRefreshActivityEvent(sweepstake.id));
    }

    /**
     * @summary Refunds a sweepstake entry set
     */
    @Delete('{sweepstakeId}/entry/set/{id}/refund')
    @LogAudit('Sweepstake Entry Set', 'Refund Sweepstake Entry Set')
    @Security('admin', ['sweepstake:entry:refund'])
    public async refund(@Path() sweepstakeId: number, @Path() id: number): Promise<void> {
        const sweepstake = await this.sweepstakeManager.get(sweepstakeId);

        if (!sweepstake)
            throw new NotFoundError('Sweepstake not found.');

        await this.sweepstakeEntrySetManager.refund(id);
        await this.eventDispatcher.send(new SweepstakeRefreshActivityEvent(sweepstake.id));
    }

    /**
     * @summary Removes a sweepstake entry set
     */
    @Delete('{sweepstakeId}/entry/set/{id}')
    @LogAudit('Sweepstake Entry Set', 'Remove Sweepstake Entry Set')
    @Security('admin', ['sweepstake:entry:delete'])
    public async remove(@Path() sweepstakeId: number, @Path() id: number): Promise<void> {
        const sweepstake = await this.sweepstakeManager.get(sweepstakeId);

        if (!sweepstake)
            throw new NotFoundError('Sweepstake not found.');

        // need a reference to the entry before it's removed to use for the balance manager
        const entry = await this.sweepstakeEntrySetManager.getById(id);
        if (!entry)
            throw new NotFoundError(`Sweepstake entry set ${id} not found.`);

        await this.sweepstakeEntrySetManager.removeById(id);
        await this.gameBuyInBalanceManager.removeEntry(entry);
        await this.eventDispatcher.send(new SweepstakeRefreshActivityEvent(sweepstake.id));
    }
}