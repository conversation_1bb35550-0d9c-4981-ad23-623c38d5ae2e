import { Inject, Singleton } from '@tcom/platform/lib/core/ioc';
import Logger, { LogClass } from '@tcom/platform/lib/core/logging';
import { BigNumber, Config, NotFoundError, ParameterStore, RequesterType } from '@tcom/platform/lib/core';
import { FireblocksTransactionApprovalStatusUpdatedNotification, FireblocksTransactionCreatedNotification, FireblocksTransactionStatusUpdatedNotification, FireblocksTransactionSubStatus } from '@tcom/platform/lib/integration/fireblocks';
import { FireblocksClientFactory, FireblocksTransactionStatus, TransferPeerPathType } from '@tcom/platform/lib/integration/fireblocks';
import { DepositAccount, DepositAccountManager, DepositProvider } from '@tcom/platform/lib/deposit';
import { WithdrawalManager, WithdrawalStatus } from '@tcom/platform/lib/withdrawal';
import { FireblocksTransactionDataProcessor, FireblocksVaultNameMapper } from '@tcom/platform/lib/deposit/providers/fireblocks';
import { NotificationProcessor } from '../notification-processor';
import { CurrencyManager, FundsContext, TransferDirection } from '@tcom/platform/lib/banking';
import { isDirectionSupported, isContextSupported, isPublicNetwork } from '@tcom/platform/lib/banking/utilities/currency-config-utility';
import { UserManager } from '@tcom/platform/lib/user';

type Notification = FireblocksTransactionCreatedNotification | FireblocksTransactionStatusUpdatedNotification | FireblocksTransactionApprovalStatusUpdatedNotification;

@Singleton
@LogClass()
export class TransactionNotificationProcessor implements NotificationProcessor<Notification> {
    constructor(
        @Inject private readonly depositAccountManager: DepositAccountManager,
        @Inject private readonly withdrawalManager: WithdrawalManager,
        @Inject private readonly vaultNameMapper: FireblocksVaultNameMapper,
        @Inject private readonly clientFactory: FireblocksClientFactory,
        @Inject private readonly transactionDataProcessor: FireblocksTransactionDataProcessor,
        @Inject private readonly currencyManager: CurrencyManager,
        @Inject private readonly parameterStore: ParameterStore,
        @Inject private readonly userManager: UserManager) {
    }

    public async process(notification: Notification): Promise<void> {
        const { id, source, destination } = notification.data;

        const [platformDepositId, platformWithdrawId, platformGasId] =
            await Promise.all([
                this.parameterStore.get(`/${Config.stage}/integration/fireblocks/deposit/vaultId`, false, true),
                this.parameterStore.get(`/${Config.stage}/integration/fireblocks/withdraw/vaultId`, false, true),
                this.parameterStore.get(`/${Config.stage}/integration/fireblocks/gas/vaultId`, false, true)
            ]);

        if (source.id === platformGasId || source.type === TransferPeerPathType.GAS_STATION) {
            await this.handleGasFunding(notification);
            return;
        }

        if (source.id === platformWithdrawId && [TransferPeerPathType.ONE_TIME_ADDRESS, TransferPeerPathType.EXTERNAL_WALLET].includes(destination.type)) {
            await this.withdrawal(notification);
            return;
        }

        if (destination.type.toUpperCase() !== TransferPeerPathType.VAULT_ACCOUNT) {
            Logger.warn(`TX notification ${id} does not have valid destination type.`);
            return;
        }

        if (this.vaultNameMapper.isUser(destination.name)) {
            await this.transactionDataProcessor.process(notification.data);
            return;
        }

        // If the notification source is from a user account to the platform vault
        // process the event as a sweep.
        if (this.vaultNameMapper.isUser(source.name) && platformDepositId === destination.id) {
            await this.handleSweep(notification);
            return;
        }

        Logger.info(`TX notification ${id} ignored.`);
    }

    private async handleGasFunding(notification: Notification): Promise<void> {
        const { source, destination, assetId } = notification.data;

        if (source.type !== TransferPeerPathType.VAULT_ACCOUNT || destination.type !== TransferPeerPathType.VAULT_ACCOUNT) {
            Logger.info('Ignoring TX notification as source or destination is not a vault account.');
            return;
        }

        if (!this.vaultNameMapper.isUser(destination.name)) {
            Logger.info('Ignoring TX notification as destination is not a user account.');
            return;
        }

        const userId = this.vaultNameMapper.fromUser(destination.name);
        let account = await this.depositAccountManager.getByAssetId(userId, assetId);

        if (!account) {
            const [currency, network] = await this.currencyManager.getByAssetId(assetId);
            const networkConfig = currency?.config?.networks?.[network];

            if (!currency.supportedContexts.includes(FundsContext.Custodial)) {
                Logger.info(`Ignoring TX notification as ${assetId} is not a custodial asset.`);
                return;
            }

            if (!networkConfig) {
                Logger.info(`Ignoring TX notification as ${assetId} does not have a network configuration.`);
                return;
            }

            const user = await this.userManager.get(userId);

            if (!user)
                throw new Error(`User ${userId} not found.`);

            if (!isContextSupported(networkConfig, FundsContext.Custodial, user.type)) {
                Logger.info(`Ignoring TX notification as ${assetId} is not a custodial asset.`);
                return;
            }

            if (!isDirectionSupported(networkConfig, TransferDirection.In, user.type)) {
                Logger.error(`Currency ${currency.code} does not support deposits on the ${network} network.`);
                return;
            }

            if (!isPublicNetwork(networkConfig, user.type)) {
                Logger.error(`Currency ${currency.code} does not support deposits on private network ${network}.`);
                return;
            }

            Logger.info(`Creating deposit account for user ${userId} asset ID ${assetId}...`);

            let destinationAddress = notification.data.destinationAddress;

            if (!notification.data.destinationAddress) {
                const client = await this.clientFactory.create();
                const depositAddresses = await client.fireblocks.getDepositAddresses(destination.id, assetId);

                if (depositAddresses.length === 0)
                    throw new NotFoundError(`No deposit addresses found for vault ${destination.id} and ${assetId} asset ID.`);

                destinationAddress = depositAddresses[0].address;
            }

            account = await this.depositAccountManager.add({
                userId,
                assetId,
                provider: DepositProvider.Fireblocks,
                vaultId: destination.id,
                currencyCode: currency.code,
                network,
                providerRef: destinationAddress
            });

            Logger.info(`Deposit account ${account.id} created.`, account);
        }

        const balance = await this.getVaultAccountBalance(account);
        await this.depositAccountManager.setBalance(account, balance);
        Logger.info(`Deposit account ${account.id} balance updated.`, { account, balance });
    }

    private async handleSweep(notification: Notification): Promise<void> {
        const { id, source, status, assetId } = notification.data;
        const resetStatuses = [
            FireblocksTransactionStatus.BLOCKED,
            FireblocksTransactionStatus.REJECTED,
            FireblocksTransactionStatus.FAILED,
            FireblocksTransactionStatus.CANCELLED
        ];

        if (![...resetStatuses, FireblocksTransactionStatus.COMPLETED].includes(status)) {
            Logger.info(`Ignoring sweep TX notification ${id} w/ ${status} status...`);
            return;
        }

        const userId = this.vaultNameMapper.fromUser(source.name);
        const account = await this.depositAccountManager.getByAssetId(userId, assetId);

        if (!account) {
            Logger.error(`Deposit Account for user ${userId} and ${assetId} asset ID not found.`);
            return;
        }

        if (notification.data.feeCurrency !== assetId) {
            const feeAccount = await this.depositAccountManager.getByAssetId(userId, notification.data.feeCurrency);

            if (!feeAccount)
                return;

            const feeAccountBalance = await this.getVaultAccountBalance(feeAccount);
            await this.depositAccountManager.setBalance(feeAccount, feeAccountBalance);
            Logger.info(`Deposit account ${feeAccount.id} balance updated.`, { feeAccount, feeAccountBalance });
        }

        if (resetStatuses.includes(status)) {
            Logger.info(`Deposit account ${account.id} sweeping reset as status was ${status}.`);
            await this.depositAccountManager.setSweeping(account, false);
            return;
        }

        if (status !== FireblocksTransactionStatus.COMPLETED) {
            Logger.info(`Ignoring sweep TX notification ${id} w/ ${status} status...`);
            return;
        }

        const balance = await this.getVaultAccountBalance(account);
        const updated = await this.depositAccountManager.swept(account, balance);

        Logger.info(`Deposit account ${updated.id} swept.`, { updated, balance });
    }

    private async withdrawal(notification: Notification): Promise<void> {
        const { id, status, subStatus, externalTxId, txHash } = notification.data;

        const ALLOWED_STATUSES = [
            FireblocksTransactionStatus.COMPLETED,
            FireblocksTransactionStatus.CANCELLED,
            FireblocksTransactionStatus.FAILED,
            FireblocksTransactionStatus.BLOCKED,
            FireblocksTransactionStatus.REJECTED,
            FireblocksTransactionStatus.PENDING_AUTHORIZATION
        ];

        if (!ALLOWED_STATUSES.includes(status)) {
            Logger.info(`Ignoring withdrawal TX notification ${id} w/ ${status} status...`);
            return;
        }

        const withdrawalId = parseInt(externalTxId, 10);

        if (isNaN(withdrawalId)) {
            Logger.error(`TX notification ${id} does not have a valid withdrawal ID.`);
            return;
        }

        if (status === FireblocksTransactionStatus.BLOCKED) {
            await this.blocked(withdrawalId);
            return;
        }

        if (status === FireblocksTransactionStatus.PENDING_AUTHORIZATION) {
            Logger.info(`Withdrawal ${id} requires authorization.`);
            await this.withdrawalManager.pendingAuthorisation(withdrawalId);
            return;
        }

        if (status === FireblocksTransactionStatus.CANCELLED && subStatus === FireblocksTransactionSubStatus.REJECTED_BY_USER) {
            await this.withdrawalManager.decline(withdrawalId, RequesterType.System, `Withdrawal ${id} is rejected by ${notification.data.rejectedBy}.`);
            return;
        }

        if (status === FireblocksTransactionStatus.FAILED && subStatus === FireblocksTransactionSubStatus.TIMEOUT) {
            Logger.info(`Withdrawal ${id} failed due to timeout.`);
            if (await this.processAuthorizeTimeout(withdrawalId))
                return;
        }

        if ([FireblocksTransactionStatus.FAILED, FireblocksTransactionStatus.REJECTED].includes(status)) {
            Logger.warn(`Withdrawal ${id} ${status.toLowerCase()}.`);
            await this.withdrawalManager.fail(withdrawalId, notification.data.subStatus);
            return;
        }

        await this.withdrawalManager.complete(withdrawalId, undefined, { providerRef: txHash ?? '', status });
        Logger.info(`Withdrawal ${withdrawalId} updated.`);
    }

    private async getVaultAccountBalance(account: DepositAccount): Promise<BigNumber> {
        const client = await this.clientFactory.create();
        const response = await client.fireblocks.getVaultAccountAsset(account.vaultId, account.assetId);
        return new BigNumber(response.total);
    }

    private async processAuthorizeTimeout(id: number): Promise<boolean> {
        const withdrawal = await this.withdrawalManager.get(id);

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        if (withdrawal.status !== WithdrawalStatus.RequiresAuthorization)
            return false;

        await this.withdrawalManager.decline(id, RequesterType.System, `Withdrawal ${id} was not authorized within the expiration window.`);
        return true;
    }

    private async blocked(id: number): Promise<void> {
        Logger.info(`Withdrawal ${id} is blocked.`);

        const withdrawal = await this.withdrawalManager.get(id);

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        if (withdrawal.status === WithdrawalStatus.Declined)
            return;

        withdrawal.status = WithdrawalStatus.Error;
        await this.withdrawalManager.decline(withdrawal, RequesterType.System, `Withdrawal ${id} was blocked.`);
    }
}