import { MigrationInterface, QueryRunner } from "typeorm";

export class SweepstakeEntrySetSource1749111135685 implements MigrationInterface {
    name = 'SweepstakeEntrySetSource1749111135685'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sweepstake_entry_set\` ADD \`source\` varchar(100) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sweepstake_entry_set\` DROP COLUMN \`source\``);
    }
}