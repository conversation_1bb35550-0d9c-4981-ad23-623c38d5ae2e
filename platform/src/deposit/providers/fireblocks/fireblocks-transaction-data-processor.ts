import { Inject, Singleton } from '../../../core/ioc';
import Logger, { LogClass } from '../../../core/logging';
import { BigNumber } from '../../../core/big-number';
import { PlatformEventDispatcher } from '../../../core/events';
import { Websocket } from '../../../websocket';
import { FundsContext } from '../../../banking/funds-context';
import { isContextSupported, isPublicNetwork, isDirectionSupported } from '../../../banking/utilities/currency-config-utility';
import { FireblocksClientFactory, FireblocksTransactionStatus, TransactionData } from '../../../integration/fireblocks';
import { Payment, PaymentMetadata, PaymentMethodType, PaymentOrigin, PaymentProvider, PaymentStatus, PaymentType } from '../../../payment';
import { PaymentManager, PaymentMethodManager } from '../../../payment';
import { DepositProvider } from '../../deposit-provider';
import { DepositAccount } from '../../deposit-account';
import { DepositAccountPaymentEvent } from '../../events';
import { DepositAccountManager } from '../../deposit-account-manager';
import { TransactionResponse } from 'fireblocks-sdk';
import { Currency, CurrencyConverter, CurrencyManager, TransferDirection } from '../../../banking';
import { BlockchainNetwork } from '../../../blockchain';
import { FireblocksVaultNameMapper } from './fireblocks-vault-name.mapper';
import { BlockchainPaymentMethodMapper } from '../../../payment/utilities';
import { Config, ParameterStore, PlatformMode } from '../../../core';
import { UserManager } from '../../../user';

@Singleton
@LogClass()
export class FireblocksTransactionDataProcessor {
    constructor(
        @Inject private readonly eventDispatcher: PlatformEventDispatcher,
        @Inject private readonly paymentManager: PaymentManager,
        @Inject private readonly depositAccountManager: DepositAccountManager,
        @Inject private readonly paymentMethodManager: PaymentMethodManager,
        @Inject private readonly paymentMethodMapper: BlockchainPaymentMethodMapper,
        @Inject private readonly vaultNameMapper: FireblocksVaultNameMapper,
        @Inject private readonly currencyManager: CurrencyManager,
        @Inject private readonly clientFactory: FireblocksClientFactory,
        @Inject private readonly websocket: Websocket,
        @Inject private readonly parameterStore: ParameterStore,
        @Inject private readonly currencyConverter: CurrencyConverter,
        @Inject private readonly userManager: UserManager) {
    }

    public async process(data: TransactionData | TransactionResponse): Promise<void> {
        const { id, assetId, destination, sourceAddress, destinationAddress, amountInfo, txHash } = data;

        if (!txHash) {
            Logger.error(`TX notification ${id} does not have an available hash.`);
            return;
        }

        if (!sourceAddress) {
            Logger.error(`TX notification ${id} does not have a source address.`);
            return;
        }

        if (!destination?.name) {
            Logger.error(`TX notification ${id} does not have a source.`);
            return;
        }

        const paymentType = Config.mode === PlatformMode.Sweepstake ? PaymentType.Purchase : PaymentType.Deposit;

        Logger.info(`Processing TX notification ${id} for ${paymentType} ${txHash}...`);

        const [currency, network] = await this.currencyManager.getByAssetId(assetId);
        const networkConfig = currency?.config?.networks?.[network];

        if (!currency.supportedContexts.includes(FundsContext.Custodial))
            throw new Error(`Currency ${currency.code} does not support custodial ${paymentType}`);

        if (!networkConfig)
            throw new Error(`Network ${network} not found for currency ${currency.code}.`);

        const userId = this.vaultNameMapper.fromUser(destination.name);
        const user = await this.userManager.get(userId);

        if (!user)
            throw new Error(`User ${userId} not found.`);

        if (!isContextSupported(networkConfig, FundsContext.Custodial, user.type))
            throw new Error(`Currency ${currency.code} does not support custodial ${paymentType} on the ${network} network.`);

        if (!isDirectionSupported(networkConfig, TransferDirection.In, user.type))
            throw new Error(`Currency ${currency.code} does not support deposits on the ${network} network.`);

        if (!isPublicNetwork(networkConfig, user.type))
            throw new Error(`Currency ${currency.code} does not support deposits on private network ${network}.`);

        let account = await this.depositAccountManager.getByAssetId(userId, assetId);

        if (!account) {
            Logger.info(`Creating deposit account for user ${userId} asset ID ${assetId}...`);

            let providerRef = destinationAddress;

            if (!providerRef) {
                const client = await this.clientFactory.create();
                const depositAddresses = await client.fireblocks.getDepositAddresses(destination.id, assetId);

                if (depositAddresses.length === 0)
                    throw new Error(`No deposit addresses found for Fireblocks vault ${destination.id} (${assetId}).`);

                providerRef = depositAddresses[0].address;
            }

            account = await this.depositAccountManager.add({
                userId,
                assetId,
                provider: DepositProvider.Fireblocks,
                vaultId: destination.id,
                currencyCode: currency.code,
                network,
                providerRef
            });

            Logger.info(`Deposit account ${account.id} created.`, account);
        }

        if (!account.enabled) {
            Logger.warn(`Ignoring TX notification ${id} for user ${account.userId} as account is disabled.`);
            return;
        }

        const paymentMethodType = this.paymentMethodMapper.toTypeFromNetwork(network);

        if (!amountInfo?.amount) {
            Logger.error(`TX notification ${id} does not have an amount.`);
            return;
        }

        const depositAmount = new BigNumber(amountInfo.amount);
        const creditAmount = depositAmount.dividedBy(currency.factor).decimalPlaces(currency.decimals);
        const aboveMinimum = await this.isAboveMinimum(creditAmount, currency, paymentType);

        if (!aboveMinimum) {
            Logger.warn(`${paymentType} ${creditAmount.formatCurrency(currency.code)} from user ${account.userId} is below the minimum threshold.`);
            return;
        }

        const provider = PaymentProvider.Fireblocks;

        if (!provider)
            throw new Error(`Payment provider for currency ${currency} not found.`);

        const existingPayment = await this.paymentManager.getByProcessorRef(provider, id);

        if (existingPayment && existingPayment.status !== PaymentStatus.Pending)
            throw Error(`Deposit ${txHash} for ${creditAmount.formatCurrency(currency.code)} from user ${account.userId} is not in a pending state.`);

        const status = this.toPaymentStatus(data.status);

        const metadata: PaymentMetadata = {};

        if (!depositAmount.eq(creditAmount))
            metadata.providerAmount = depositAmount.toString();

        if (currency.baseCode)
            metadata.providerCurrencyCode = currency.baseCode;

        const payment = existingPayment
            ? await this.paymentManager.update({ ...existingPayment, status, metadata: { ...existingPayment.metadata, ...metadata } })
            : await this.addPayment(data, creditAmount, paymentMethodType, provider, status, txHash, network, sourceAddress, account, currency.code, metadata);

        switch (payment.status) {
            case PaymentStatus.Pending:
                await this.sendPendingMessage(payment);
                break;

            case PaymentStatus.Successful:
                await this.setAccountBalance(account, payment);
                await this.sendCompleteMessage(payment, account);
                break;
        }

        Logger.info(`TX notification ${id} processed successfully.`, payment);
    }

    private toPaymentStatus(status: string): PaymentStatus {
        switch (status) {
            case FireblocksTransactionStatus.COMPLETED:
                return PaymentStatus.Successful;

            case FireblocksTransactionStatus.BLOCKED:
            case FireblocksTransactionStatus.FAILED:
            case FireblocksTransactionStatus.CANCELLED:
            case FireblocksTransactionStatus.REJECTED:
                return PaymentStatus.Declined;

            default:
                return PaymentStatus.Pending;
        }
    }

    private async addPayment(
        data: TransactionData | TransactionResponse,
        amount: BigNumber,
        paymentMethodType: PaymentMethodType,
        provider: PaymentProvider,
        status: PaymentStatus,
        providerRef: string,
        network: BlockchainNetwork,
        paymentMethodRef: string,
        account: DepositAccount,
        currencyCode: string,
        metadata?: PaymentMetadata
    ): Promise<Payment> {
        const paymentMethod = await this.paymentMethodManager.getOrAdd(paymentMethodType, provider, PaymentType.Deposit, paymentMethodRef, account.userId, false);

        const paymentType = Config.mode === PlatformMode.Sweepstake ? PaymentType.Purchase : PaymentType.Deposit;

        return this.paymentManager.add({
            type: paymentType,
            origin: PaymentOrigin.Fireblocks,
            fundsContext: FundsContext.Custodial,
            status,
            provider,
            providerRef,
            providerNetwork: network,
            paymentMethodId: paymentMethod.id,
            amount,
            currencyCode,
            userId: paymentMethod.userId,
            memo: `Fireblocks ${paymentType} - ${data.id}`,
            processorRef: data.id,
            createTime: new Date(data.createdAt),
            metadata: {
                ...metadata,
                depositAccount: {
                    provider: DepositProvider.Fireblocks,
                    vaultId: account.vaultId,
                    assetId: account.assetId
                }
            }
        });
    }

    private async setAccountBalance(account: DepositAccount, payment: Payment): Promise<void> {
        const balance = await this.getVaultAccountBalance(account);

        const updated = await this.depositAccountManager.deposited(account, payment.id, balance);
        Logger.info(`Deposit account ${updated.id} updated.`, { updated, balance });
    }

    private async getVaultAccountBalance(account: DepositAccount): Promise<BigNumber> {
        const client = await this.clientFactory.create();
        const response = await client.fireblocks.getVaultAccountAsset(account.vaultId, account.assetId);
        let balance = new BigNumber(response.total);

        const currency = await this.currencyManager.get(account.currencyCode);
        if (currency)
            balance = balance.dividedBy(currency.factor).decimalPlaces(currency.decimals);
        else
            Logger.warn(`Cannot locate currency ${account.currencyCode} used by deposit account ${account.id}.`);

        return new BigNumber(balance);
    }

    private async sendPendingMessage(payment: Payment): Promise<void> {
        await this.websocket
            .user(payment.userId)
            .type('Deposit:Pending')
            .payload({
                id: payment.id,
                currencyCode: payment.currencyCode,
                amount: payment.amount,
                provider: payment.provider,
                providerRef: payment.providerRef,
                providerNetwork: payment.providerNetwork,
                fundsContext: payment.fundsContext
            })
            .send();
    }

    private async sendCompleteMessage(payment: Payment, account: DepositAccount): Promise<void> {
        await this.eventDispatcher.send(new DepositAccountPaymentEvent({
            userId: account.userId,
            payment: {
                id: payment.id,
                status: payment.status,
                currencyCode: payment.currencyCode,
                amount: payment.amount,
            },
            deposit: {
                accountId: account.id,
                provider: account.provider
            }
        }));

        await this.websocket
            .user(payment.userId)
            .type('Deposit:Complete')
            .payload({
                id: payment.id,
                currencyCode: payment.currencyCode,
                amount: payment.amount,
                provider: payment.provider,
                providerRef: payment.providerRef,
                providerNetwork: payment.providerNetwork,
                status: payment.status,
                fundsContext: payment.fundsContext
            })
            .send();
    }

    private async isAboveMinimum(amount: BigNumber, currency: Currency, paymentType: PaymentType): Promise<boolean> {
        if (paymentType === PaymentType.Deposit)
            return amount.isGreaterThanOrEqualTo(currency.config?.deposit?.minAmount || 0);

        const minThreshold = await this.parameterStore.get(`/${Config.stage}/payment/purchase/minimum`, false, true);
        const conversion = await this.currencyConverter.convert(currency.code, Config.baseCurrency, amount);
        return conversion.amountConverted.isGreaterThanOrEqualTo(minThreshold);
    }
}