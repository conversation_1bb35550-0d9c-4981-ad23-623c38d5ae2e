import { Inject, Singleton } from '../../../core/ioc';
import Logger, { LogClass } from '../../../core/logging';
import { UserManager } from '../../../user/user-manager';
import { PaymentManager, PaymentMethodManager, PaymentConfigurationManager, Payment, PaymentStatus, PaymentProvider, PaymentType, PaymentHoldEvaluator } from '../../../payment';
import { Order, OrderManager, OrderStatus } from '../../../order';
import { Websocket } from '../../../websocket';
import { EthereumPaymentCache } from '../../../payment/providers/ethereum';
import { BlockchainEventProvider, BlockchainNotification } from '../../../integration/blockchain/events';
import { UserAliasType } from '../../../user';
import { BigNumber, Config, ParameterStore } from '../../../core';
import { CryptoCurrency, CurrencyManager, CurrencyType, FundsContext, TransferDirection } from '../../../banking';
import { isDirectionSupported, isContextSupported, isPublicNetwork } from '../../../banking/utilities/currency-config-utility';
import { BlockchainNetwork } from '../../../blockchain/blockchain-network';
import { EthereumClientFactory } from '../ethereum';

interface PaymentInfo {
    payment: Payment;
    order?: Order;
}

@Singleton
@LogClass()
export class BlockchainPaymentProcessor {
    constructor(
        @Inject private readonly parameterStore: ParameterStore,
        @Inject private readonly paymentManager: PaymentManager,
        @Inject private readonly paymentMethodManager: PaymentMethodManager,
        @Inject private readonly paymentCurrencyConfig: PaymentConfigurationManager,
        @Inject private readonly orderManager: OrderManager,
        @Inject private readonly websocket: Websocket,
        @Inject private readonly cache: EthereumPaymentCache,
        @Inject private readonly ethereumClientFactory: EthereumClientFactory,
        @Inject private readonly userManager: UserManager,
        @Inject private readonly currencyManager: CurrencyManager,
        @Inject private readonly holdEvaluator: PaymentHoldEvaluator) {
    }

    public async process<T extends BlockchainNotification>(provider: BlockchainEventProvider, data: Readonly<T>): Promise<void> {
        const platformAddresses = await this.getPlatformAddresses(data.network);

        if (platformAddresses.some(p => p.toLowerCase() === data.from.toLowerCase())) {
            Logger.warn(`Source address ${data.from} is reserved and should not be processed.`);
            return;
        }

        const blacklistedAddresses = await this.getBlacklistedAddresses(data.network);

        if (blacklistedAddresses.some(b => b.toLowerCase() === data.from.toLowerCase())) {
            Logger.warn(`Source address ${data.from} is blacklisted and should not be processed.`);
            return;
        }

        const info = await this.getPaymentInfo(data);

        if (info?.order) {
            await this.processOrder(info.payment, info.order, data.network);
            return;
        }

        await this.processDeposit(provider, data);
    }

    private async processOrder(payment: Payment, order: Order, network: BlockchainNetwork): Promise<void> {
        if (payment.status !== PaymentStatus.Pending) {
            Logger.error(`Payment ${payment.id} was in a ${payment.status} state, expected Pending.`);
            return;
        }

        if (order.status !== OrderStatus.PendingPayment) {
            Logger.error(`Order ${order.id} should have a status of PendingPayment but had a status of ${order.status}.`);
            return;
        }

        payment.status = PaymentStatus.Successful;
        payment.providerNetwork = network;
        await this.paymentManager.update(payment);
        await this.orderManager.setStatus(order.id, OrderStatus.Paid);
    }

    private async processDeposit(provider: BlockchainEventProvider, notification: BlockchainNotification): Promise<void> {
        const { network } = notification;

        const paymentProvider = notification.network === BlockchainNetwork.Solana
            ? PaymentProvider.Solana
            : PaymentProvider.Ethereum;

        const aliasType = network === BlockchainNetwork.Solana
            ? UserAliasType.SolanaAddress
            : UserAliasType.EthereumAddress;

        const existingPayment = await this.paymentManager.getByProviderRef(paymentProvider, notification.hash);

        if (existingPayment) {
            Logger.info(`Deposit for transaction ${notification.hash} already exists, ignoring.`);
            return;
        }

        const user = await this.userManager.getByAlias(aliasType, notification.from);

        if (!user) {
            Logger.warn(`Transaction received from unknown source: ${notification.from} ${notification.value}`);
            return;
        }

        if (user.metadata?.ignorePayments) {
            Logger.warn(`Transaction recieved for user ${user.id}, ignoring due to metadata.`);
            return;
        }

        const paymentMethod = await this.paymentMethodManager.getByProviderRef(paymentProvider, notification.from, user.id);

        if (!paymentMethod) {
            Logger.error(`Payment method ${notification.from} for user ${user.id} not found.`);
            return;
        }

        if (!notification.category || !['internal', 'external', 'token', 'erc20'].includes(notification.category)) {
            Logger.error(`Transaction received with unsupported category: ${notification.category}`);
            return;
        }

        let currencyCode = notification.asset || 'ETH';
        let currency: CryptoCurrency | undefined;

        if (notification.category === 'token' || notification.category === 'erc20') {
            if (!notification.contractAddress) {
                Logger.error(`Transaction received for token '${notification.asset}' with unknown contract address.`);
                return;
            }

            currency = await this.currencyManager.getByNetworkAddress(network, notification.contractAddress);

            if (!currency) {
                Logger.error(`Transaction received for token '${notification.asset}' with unsupported contract address '${notification.contractAddress}'.`);
                return;
            }

            if (!currency.supportedContexts.includes(FundsContext.Default)) {
                Logger.error(`Deposit for token '${notification.asset}' not supported by context.`);
                return;
            }

            currencyCode = currency.code;
        } else {
            const targetCurrency = await this.currencyManager.get(currencyCode);

            if (targetCurrency?.type !== CurrencyType.Crypto) {
                Logger.error(`Transaction received for unsupported currency ${currencyCode}.`);
                return;
            }

            currency = targetCurrency;
        }

        if (!currency) {
            Logger.error(`Currency ${currencyCode} not found.`);
            return;
        }

        const networkConfig = currency?.config?.networks?.[network];

        if (!networkConfig)
            throw new Error(`Network ${network} not found for currency ${currencyCode}.`);

        if (!isContextSupported(networkConfig, FundsContext.Default, user.type))
            throw new Error(`Currency ${currency.code} does not support default context deposits on the ${network} network.`);

        if (!isDirectionSupported(networkConfig, TransferDirection.In, user.type))
            throw new Error(`Currency ${currency.code} does not support deposits on the ${network} network.`);

        if (!isPublicNetwork(networkConfig, user.type))
            throw new Error(`Currency ${currency.code} does not support deposits on private network ${network}.`);

        const depositAmount = provider === BlockchainEventProvider.Blocknative
            ? new BigNumber(notification.value).shiftedBy(-18)
            : new BigNumber(notification.value);

        const creditAmount = depositAmount.dividedBy(currency.factor).decimalPlaces(currency.decimals);
        const minThreshold = await this.paymentCurrencyConfig.getMinDepositThreshold(currencyCode);

        if (creditAmount.isLessThan(minThreshold)) {
            Logger.warn(`Deposit ${creditAmount.formatCurrency(currencyCode)} from user ${user.id} is below the minimum threshold.`);
            return;
        }

        let metadata = await this.cache.getPendingTransactionMetadata(notification.hash);

        metadata = metadata ?? {};

        if (!depositAmount.eq(creditAmount))
            metadata.providerAmount = depositAmount.toString();

        if (currency.baseCode)
            metadata.providerCurrencyCode = currency.baseCode;

        const holdEvaluation = await this.holdEvaluator.evaluate(user.id, PaymentType.Deposit, currencyCode, creditAmount);

        const paymentStatus = holdEvaluation.hold ? PaymentStatus.OnHold : PaymentStatus.Successful;
        const holdReason = holdEvaluation.hold ? holdEvaluation.reason : undefined;

        const payment = await this.paymentManager.add({
            userId: paymentMethod.userId,
            paymentMethodId: paymentMethod.id,
            amount: creditAmount,
            currencyCode,
            provider: paymentProvider,
            providerRef: notification.hash,
            providerNetwork: notification.network,
            status: paymentStatus,
            fundsContext: notification.fundsContext ?? FundsContext.Default,
            type: PaymentType.Deposit,
            createTime: new Date(),
            memo: `Deposit from ${network}`,
            metadata,
            holdReason
        });

        await this.notifyDepositComplete(payment);
    }

    private async notifyDepositComplete(payment: Payment): Promise<void> {
        await this.websocket.user(payment.userId)
            .type('Deposit:Complete')
            .payload({
                id: payment.id,
                currencyCode: payment.currencyCode,
                amount: payment.amount,
                provider: payment.provider,
                providerRef: payment.providerRef,
                providerNetwork: payment.providerNetwork,
                status: payment.status,
                fundsContext: payment.fundsContext
            })
            .send();
    }

    private async getPaymentInfo(notification: BlockchainNotification): Promise<PaymentInfo | undefined> {
        const { network } = notification;

        if (network !== BlockchainNetwork.Ethereum) {
            Logger.info(`Network is not Ethereum, ignoring...`, network);
            return undefined;
        }

        const payment = await this.paymentManager.getByProviderRef(PaymentProvider.Ethereum, notification.hash);

        if (payment)
            return {
                payment,
                order: await this.orderManager.getByPaymentId(payment.id)
            };

        Logger.warn(`Could not get pending Ethereum payment with reference ${notification.hash}, checking for pending transaction with matching nonce...`);

        const client = await this.ethereumClientFactory.create(notification.network);
        const transaction = await client.getTransaction(notification.hash);

        Logger.info(`Found ETH Transaction`, transaction);

        const pendingTransaction = await this.cache.getPendingTransaction(transaction.from, transaction.nonce);

        if (!pendingTransaction)
            return undefined;

        Logger.info('Cached Pending Transaction', pendingTransaction);

        const replacedPayment = await this.paymentManager.getByProviderRef(PaymentProvider.Ethereum, pendingTransaction.hash);

        Logger.info('Replaced Payment', replacedPayment);

        if (!replacedPayment)
            return;

        replacedPayment.providerRef = notification.hash;
        await this.paymentManager.update(replacedPayment);

        Logger.info('Payment Reference Updated');

        return { payment: replacedPayment };
    }

    private async getPlatformAddresses(network: BlockchainNetwork): Promise<string[]> {
        if (network === BlockchainNetwork.Solana)
            return this.parameterStore.getList(`/${Config.stage}/crypto/solana/address/reserved`, false);
        else
            return this.parameterStore.getList(`/${Config.stage}/crypto/ethereum/address/reserved`, false);
    }

    private async getBlacklistedAddresses(network: BlockchainNetwork): Promise<string[]> {
        if (network === BlockchainNetwork.Solana)
            return this.parameterStore.getList(`/${Config.stage}/crypto/solana/address/blacklist`, false);
        else
            return this.parameterStore.getList(`/${Config.stage}/crypto/ethereum/address/blacklist`, false);
    }
}