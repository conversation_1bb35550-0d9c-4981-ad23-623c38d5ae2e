import { Inject, Singleton } from '../../../core/ioc';
import Logger, { LogClass } from '../../../core/logging';
import { UserManager } from '../../../user/user-manager';
import { PaymentManager, PaymentMethodManager, PaymentStatus, PaymentProvider, PaymentType, PaymentHoldEvaluator } from '../../../payment';
import { EthereumPaymentCache } from '../../../payment/providers/ethereum';
import { BlockchainEventProvider, BlockchainNotification } from '../../../integration/blockchain/events';
import { UserAliasType } from '../../../user';
import { BigNumber, Config, ParameterStore } from '../../../core';
import { CryptoCurrency, CurrencyConverter, CurrencyManager, CurrencyType, FundsContext, TransferDirection } from '../../../banking';
import { isDirectionSupported, isContextSupported, isPublicNetwork } from '../../../banking/utilities/currency-config-utility';
import { BlockchainNetwork } from '../../../blockchain/blockchain-network';

@Singleton
@LogClass()
export class BlockchainPurchaseProcessor {
    constructor(
        @Inject private readonly parameterStore: ParameterStore,
        @Inject private readonly paymentManager: PaymentManager,
        @Inject private readonly paymentMethodManager: PaymentMethodManager,
        @Inject private readonly cache: EthereumPaymentCache,
        @Inject private readonly userManager: UserManager,
        @Inject private readonly currencyManager: CurrencyManager,
        @Inject private readonly currencyConverter: CurrencyConverter,
        @Inject private readonly holdEvaluator: PaymentHoldEvaluator) {
    }

    public async process<T extends BlockchainNotification>(provider: BlockchainEventProvider, data: Readonly<T>): Promise<void> {
        const platformAddresses = await this.getPlatformAddresses(data.network);

        if (platformAddresses.some(p => p.toLowerCase() === data.from.toLowerCase())) {
            Logger.warn(`Source address ${data.from} is reserved and should not be processed.`);
            return;
        }

        const blacklistedAddresses = await this.getBlacklistedAddresses(data.network);

        if (blacklistedAddresses.some(b => b.toLowerCase() === data.from.toLowerCase())) {
            Logger.warn(`Source address ${data.from} is blacklisted and should not be processed.`);
            return;
        }

        await this.processPurchase(provider, data);
    }

    private async processPurchase(provider: BlockchainEventProvider, notification: BlockchainNotification): Promise<void> {
        const { network } = notification;
        const paymentProvider = notification.network === BlockchainNetwork.Solana ? PaymentProvider.Solana : PaymentProvider.Ethereum;
        const aliasType = network === BlockchainNetwork.Solana ? UserAliasType.SolanaAddress : UserAliasType.EthereumAddress;

        const existingPayment = await this.paymentManager.getByProviderRef(paymentProvider, notification.hash);

        if (existingPayment) {
            Logger.info(`Payment for transaction ${notification.hash} already exists, ignoring.`);
            return;
        }

        const user = await this.userManager.getByAlias(aliasType, notification.from);

        if (!user) {
            Logger.error(`Transaction received from unknown source: ${notification.from} ${notification.value}`);
            return;
        }

        const paymentMethod = await this.paymentMethodManager.getByProviderRef(paymentProvider, notification.from, user.id);

        if (!paymentMethod) {
            Logger.error(`Payment method ${notification.from} for user ${user.id} not found.`);
            return;
        }

        if (!notification.category || !['internal', 'external', 'token', 'erc20'].includes(notification.category)) {
            Logger.error(`Transaction received with unsupported category: ${notification.category}`);
            return;
        }

        let currencyCode = notification.asset || 'ETH';
        let currency: CryptoCurrency | undefined;

        if (notification.category === 'token' || notification.category === 'erc20') {
            if (!notification.contractAddress) {
                Logger.error(`Transaction received for token '${notification.asset}' with unknown contract address.`);
                return;
            }

            currency = await this.currencyManager.getByNetworkAddress(network, notification.contractAddress);

            if (!currency) {
                Logger.error(`Transaction received for token '${notification.asset}' with unsupported contract address '${notification.contractAddress}'.`);
                return;
            }

            if (!currency.supportedContexts.includes(FundsContext.Default)) {
                Logger.error(`Purchase with token '${notification.asset}' not supported by context.`);
                return;
            }

            currencyCode = currency.code;
        } else {
            const targetCurrency = await this.currencyManager.get(currencyCode);

            if (targetCurrency?.type !== CurrencyType.Crypto) {
                Logger.error(`Transaction received for unsupported currency ${currencyCode}.`);
                return;
            }

            currency = targetCurrency;
        }

        if (!currency) {
            Logger.error(`Currency ${currencyCode} not found.`);
            return;
        }

        const networkConfig = currency?.config?.networks?.[network];

        if (!networkConfig)
            throw new Error(`Network ${network} not found for currency ${currencyCode}.`);

        if (!isContextSupported(networkConfig, FundsContext.Default, user.type))
            throw new Error(`Currency ${currency.code} does not support default context for purchases on the ${network} network.`);

        if (!isDirectionSupported(networkConfig, TransferDirection.In, user.type))
            throw new Error(`Currency ${currency.code} does not support purchases on the ${network} network.`);

        if (!isPublicNetwork(networkConfig, user.type))
            throw new Error(`Currency ${currency.code} does not support purchases on private network ${network}.`);

        const incomingAmount = provider === BlockchainEventProvider.Blocknative
            ? new BigNumber(notification.value).shiftedBy(-18)
            : new BigNumber(notification.value);

        const purchaseAmount = incomingAmount.dividedBy(currency.factor).decimalPlaces(currency.decimals);
        const aboveMinimum = await this.isAboveMinimum(purchaseAmount, currencyCode);

        if (!aboveMinimum) {
            Logger.warn(`Purchase amount of ${purchaseAmount.formatCurrency(currencyCode)} from user ${user.id} is below the minimum threshold.`);
            return;
        }

        let metadata = await this.cache.getPendingTransactionMetadata(notification.hash);

        metadata = metadata ?? {};

        if (!incomingAmount.eq(purchaseAmount))
            metadata.providerAmount = incomingAmount.toString();

        if (currency.baseCode)
            metadata.providerCurrencyCode = currency.baseCode;

        const holdEvaluation = await this.holdEvaluator.evaluate(user.id, PaymentType.Purchase, currencyCode, purchaseAmount);

        const paymentStatus = holdEvaluation.hold ? PaymentStatus.OnHold : PaymentStatus.Successful;
        const holdReason = holdEvaluation.hold ? holdEvaluation.reason : undefined;

        if (holdEvaluation.hold)
            metadata.promotionsDisabled = user.metadata?.promotionsDisabled ?? false;

        await this.paymentManager.add({
            userId: paymentMethod.userId,
            paymentMethodId: paymentMethod.id,
            amount: purchaseAmount,
            currencyCode,
            provider: paymentProvider,
            providerRef: notification.hash,
            providerNetwork: notification.network,
            status: paymentStatus,
            fundsContext: notification.fundsContext ?? FundsContext.Default,
            type: PaymentType.Purchase,
            createTime: new Date(),
            memo: `Purchase from ${network}`,
            metadata,
            holdReason
        });
    }

    private async getPlatformAddresses(network: BlockchainNetwork): Promise<string[]> {
        if (network === BlockchainNetwork.Solana)
            return this.parameterStore.getList(`/${Config.stage}/crypto/solana/address/reserved`, false);
        else
            return this.parameterStore.getList(`/${Config.stage}/crypto/ethereum/address/reserved`, false);
    }

    private async getBlacklistedAddresses(network: BlockchainNetwork): Promise<string[]> {
        if (network === BlockchainNetwork.Solana)
            return this.parameterStore.getList(`/${Config.stage}/crypto/solana/address/blacklist`, false);
        else
            return this.parameterStore.getList(`/${Config.stage}/crypto/ethereum/address/blacklist`, false);
    }

    private async isAboveMinimum(amount: BigNumber, currencyCode: string): Promise<boolean> {
        const minThreshold = await this.parameterStore.get(`/${Config.stage}/payment/purchase/minimum`, false, true);
        const conversion = await this.currencyConverter.convert(currencyCode, Config.baseCurrency, amount);
        return conversion.amountConverted.isGreaterThanOrEqualTo(minThreshold);
    }
}