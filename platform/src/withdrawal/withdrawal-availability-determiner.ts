import { LogClass } from '../core/logging';
import { Inject, Singleton } from '../core/ioc';
import { getEnumValue, isEqualIgnoreCase } from '../core/utilities';
import { UserManager, UserType, UserVerificationStatus } from '../user';
import { WithdrawalAvailabilityModel, WithdrawalFeeModel } from './models';
import { WalletAccountManager, CurrencyManager, FundsContext, UserWalletAccountResolver, CurrencyConverter, Currency, CurrencyType, TransferDirection } from '../banking';
import { isDirectionSupported, isPublicNetwork } from '../banking/utilities/currency-config-utility';
import { WithdrawalConfigurationRetriever } from './withdrawal-configuration-retriever';
import { BigNumber, Config } from '../core';
import { WithdrawalAvailabilityOutcome } from './withdrawal-availability-outcome';
import { WithdrawalFeeConfiguration, WithdrawalFeeType } from './withdrawal-fee-configuration';
import { WithdrawalNotPermittedError } from './errors';
import { WithdrawalManager } from './withdrawal-manager';
import { WithdrawalStatus } from './withdrawal-status';
import { PaymentStatisticsManager } from '../payment';
import { RewardManager, RewardRestrictionType } from '../reward';
import { WithdrawalLimitDeterminer } from './withdrawal-limit-determiner';
import { WithdrawalCurrencyConfiguration } from './withdrawal-currency-configuration';
import { WithdrawalSystemManager } from './withdrawal-system-manager';
import { WithdrawalSystemStatus } from './withdrawal-system-status';
import { BlockchainNetwork } from '../blockchain/blockchain-network';
import _ from 'lodash';

const MAX_PENDING = 1;

@Singleton
@LogClass()
export class WithdrawalAvailabilityDeterminer {
    constructor(
        @Inject private readonly currencyManager: CurrencyManager,
        @Inject private readonly userManager: UserManager,
        @Inject private readonly withdrawalManager: WithdrawalManager,
        @Inject private readonly limitDeterminer: WithdrawalLimitDeterminer,
        @Inject private readonly walletManager: WalletAccountManager,
        @Inject private readonly configurationRetriever: WithdrawalConfigurationRetriever,
        @Inject private readonly accountResolver: UserWalletAccountResolver,
        @Inject private readonly paymenStatisticsManager: PaymentStatisticsManager,
        @Inject private readonly rewardManager: RewardManager,
        @Inject private readonly currencyConverter: CurrencyConverter,
        @Inject private readonly systemManager: WithdrawalSystemManager) {
    }

    public async determine(userId: number, currencyCode: string, amount?: BigNumber, fundsContext?: FundsContext, providerNetwork?: string): Promise<WithdrawalAvailabilityModel> {
        const systemInfo = await this.systemManager.getInfo();

        if (systemInfo.status === WithdrawalSystemStatus.Stopped)
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                currency: currencyCode,
                detail: 'Withdrawal system is currently unavailable.'
            });

        const user = await this.userManager.get(userId);
        if (!user)
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                currency: currencyCode,
                detail: 'User not found.'
            });

        if (!user.enabled || (user.type !== UserType.Standard && !user.metadata?.internalWithdrawalOverride))
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                currency: currencyCode,
                detail: 'User account is not allowed to withdraw.'
            });

        const currency = await this.currencyManager.get(currencyCode);
        if (!currency || currency.virtual)
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                currency: currencyCode,
                detail: 'Currency is not supported.'
            });

        if (currency.type === CurrencyType.Crypto && currency.config) {
            let network: BlockchainNetwork | undefined;

            if (providerNetwork)
                network = getEnumValue(BlockchainNetwork, providerNetwork);

            if (!network)
                network = currency.config.defaultNetwork;

            const networkConfig = currency.config?.networks?.[network];

            if (!networkConfig)
                return this.buildOutcome({
                    outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                    currency: currencyCode,
                    detail: 'Withdrawals are not supported on this network.'
                });

            if (!isDirectionSupported(networkConfig, TransferDirection.Out, user.type))
                return this.buildOutcome({
                    outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                    currency: currencyCode,
                    detail: 'Withdrawals are not supported on this network.'
                });

            if (!isPublicNetwork(networkConfig, user.type))
                return this.buildOutcome({
                    outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                    currency: currencyCode,
                    detail: 'Withdrawals are not supported on this network.'
                });
        }

        const source = await this.accountResolver.resolve(currency.code, user, fundsContext);
        const currencyConfiguration = await this.configurationRetriever.retrieve(currency.code, source.context, user);
        if (!currencyConfiguration)
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                currency: currencyCode,
                detail: 'Currency is not available for withdrawal.'
            });

        if (currencyConfiguration.verificationRequired && user.amlStatus === UserVerificationStatus.Failed)
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                currency: currencyCode,
                detail: 'Verification failed.'
            });

        if ((currencyConfiguration.verificationRequired || user.tags?.includes(`VerificationRequired-${fundsContext}`)) &&
            (user.identityStatus !== UserVerificationStatus.Passed ||
                user.addressStatus !== UserVerificationStatus.Passed ||
                user.amlStatus !== UserVerificationStatus.Passed))
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.VerificationRequired,
                currency: currencyCode,
                detail: 'Verification is required.'
            });

        const pendingWithdrawals = await this.withdrawalManager.getCount({
            userId,
            statuses: [WithdrawalStatus.Pending, WithdrawalStatus.Confirmed, WithdrawalStatus.Approved, WithdrawalStatus.RequiresAuthorization, WithdrawalStatus.Processing, WithdrawalStatus.InsufficientFunds],
            currencyCode: currencyConfiguration.currencyCode === Config.baseCurrency ? undefined : currency.code,
            fundsContext: currencyConfiguration.limitsIgnoreContext ? undefined : source.context
        });

        if (pendingWithdrawals >= MAX_PENDING)
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.MaximumPendingWithdrawals,
                currency: currencyCode,
                detail: `Pending ${pendingWithdrawals === 1 ? 'withdrawal' : 'withdrawals'} found, please wait for them to complete.`,
                canCancel: currencyConfiguration.userCancellable,
                requireConfirm: currencyConfiguration.userConfirmable
            });

        if (currencyConfiguration.emailRequired) {
            const profile = await this.userManager.getProfile(userId);
            if (!profile || !profile.emailVerified)
                return this.buildOutcome({
                    outcome: WithdrawalAvailabilityOutcome.VerifiedEmailRequired,
                    currency: currencyCode,
                    detail: 'You must have a verified email address before you can withdraw.',
                });
        }

        if (user.tags?.includes(`VerificationRequired-${fundsContext}`) && user.identityStatus !== UserVerificationStatus.Passed)
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.VerificationRequired,
                currency: currencyCode,
                detail: 'Your account is currently under review.'
            });

        if (currencyConfiguration.depositRequired) {
            const deposits = await this.getDepositCount(userId, currency.code, source.context, currencyConfiguration.depositRequired.contextOnly);
            if (deposits === 0)
                return this.buildOutcome({
                    outcome: WithdrawalAvailabilityOutcome.DepositRequirementNotMet,
                    currency: currencyCode,
                    detail: `You must have made a deposit to ${currencyConfiguration.depositRequired.contextOnly ? 'one of your custodial balances' : `your custodial ${currency.code} balance`} before you can withdraw.`,
                });
        }

        const rewards = await this.rewardManager.getPending({
            userId,
            cacheOnly: false,
            restrictions: [
                RewardRestrictionType.Withdrawal
            ]
        });

        if (rewards.length > 0)
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.PendingRewardRestriction,
                currency: currencyCode,
                detail: `You cannot withdraw ${currencyCode} while you have a pending reward on your account.`,
            });

        const conversionRate = await this.currencyConverter.getRate(currencyConfiguration.currencyCode, currency.code);
        if (!conversionRate)
            return this.buildOutcome({
                outcome: WithdrawalAvailabilityOutcome.NotPermitted,
                currency: currencyCode,
                detail: 'Currency rate is not available.'
            });

        const account = await this.walletManager.getForUser(userId, source.account, currency.code);
        const accountBalance = account?.balance ?? new BigNumber(0);
        const targetDays = currencyConfiguration.targetDays;
        const minAmount = this.getMinimumAmount(currencyConfiguration, conversionRate);
        const defaultAmounts = this.getDefaultAmounts(currencyConfiguration, conversionRate);
        const targetAmount = amount ?? minAmount ?? defaultAmounts[0];
        const fee = this.getFee(targetAmount, currency, conversionRate, currencyConfiguration.fee);
        const netAmount = this.calculateNetAmount(targetAmount, fee);
        let outcome = WithdrawalAvailabilityOutcome.Available;
        let detail: string | undefined;

        if (netAmount.isLessThanOrEqualTo(0)) {
            outcome = WithdrawalAvailabilityOutcome.MinimumBalanceRequired;
            detail = 'The withdrawal will result in no funds being sent to you. Please increase the amount you want to withdraw and try again.';
        }

        if (targetAmount.isGreaterThan(accountBalance)) {
            outcome = WithdrawalAvailabilityOutcome.InsufficientBalance;
            detail = `Requested withdrawal amount ${targetAmount} ${currency.code} exceeds current balance.`;
        }

        if (minAmount && targetAmount.isLessThan(minAmount)) {
            outcome = WithdrawalAvailabilityOutcome.MinimumWithdrawalNotMet;
            detail = `Requested withdrawal amount is below minimum ${minAmount} ${currency.code}.`;
        }

        const limits = await this.limitDeterminer.determine(user, currencyCode, source.context);

        let isMonthlyLimit = false;

        if (limits.monthlyLimitRemaining && limits.limitRemaining && limits.monthlyLimitRemaining.isLessThan(limits.limitRemaining))
            isMonthlyLimit = true;

        const totalLimit = limits.limit;
        const monthlyTotalLimit = limits.monthlyLimit;

        const remaining = limits.limitRemaining;
        const monthlyRemaining = limits.monthlyLimitRemaining;

        let limitExpiryTime: Date | undefined;
        let limitRemainingAfterExpiry: BigNumber | undefined = limits.limitRemainingAfterExpiry;

        if (remaining && remaining.minus(targetAmount).isLessThan(0)) {
            outcome = WithdrawalAvailabilityOutcome.WithdrawalLimitReached;
            detail = `Requested withdrawal amount ${targetAmount} ${currencyCode} will exceed withdrawal limit.`;

            limitExpiryTime = limits.limitExpiryTime;
            limitRemainingAfterExpiry = limits.limitRemainingAfterExpiry;
        }

        if (monthlyRemaining && monthlyRemaining.minus(targetAmount).isLessThan(0)) {
            outcome = WithdrawalAvailabilityOutcome.WithdrawalLimitReached;
            detail = `Requested withdrawal amount ${targetAmount} ${currencyCode} will exceed the monthly withdrawal limit.`;

            limitExpiryTime = limits.monthlyLimitExpiryTime;
            limitRemainingAfterExpiry = limits.monthlyLimitRemainingAfterExpiry;
        }

        const decimals = Math.min(currency.decimals, 8);

        return this.buildOutcome({
            currency: currency.code,
            fundsContext: source.context,
            outcome,
            detail,
            availableAmount: accountBalance,
            amount: targetAmount,
            netAmount,
            fee,
            minimumAmount: minAmount?.decimalPlaces(decimals),
            availableInDays: targetDays,
            defaultAmounts,
            canCancel: currencyConfiguration.userCancellable,
            requireConfirm: currencyConfiguration.userConfirmable,
            limit: isMonthlyLimit ? limits.monthlyBaseLimit : limits.baseLimit,
            totalLimit: isMonthlyLimit ? monthlyTotalLimit?.decimalPlaces(decimals) : totalLimit?.decimalPlaces(decimals),
            limitRemaining: isMonthlyLimit ? monthlyRemaining?.decimalPlaces(decimals) : remaining?.decimalPlaces(decimals),
            limitPeriodInHours: !isMonthlyLimit ? currencyConfiguration.maxLimit?.periodInHours : undefined,
            limitExpiryTime,
            limitRemainingAfterExpiry: limitRemainingAfterExpiry?.decimalPlaces(decimals),
            isMonthlyLimit: isMonthlyLimit || undefined
        });
    }

    private getMinimumAmount(configuration: WithdrawalCurrencyConfiguration, conversionRate: BigNumber): BigNumber | undefined {
        if (!configuration.minAmount)
            return;

        return configuration.minAmount.times(conversionRate);
    }

    private getDefaultAmounts(configuration: WithdrawalCurrencyConfiguration, conversionRate: BigNumber): BigNumber[] {
        return configuration.defaultAmounts.map(r => new BigNumber(r.times(conversionRate).toPrecision(3, BigNumber.ROUND_UP)));
    }

    private getFee(totalAmount: BigNumber, currency: Currency, conversionRate: BigNumber, configuration?: WithdrawalFeeConfiguration): WithdrawalFeeModel | undefined {
        if (!configuration)
            return undefined;

        const decimals = Math.min(currency.decimals, 8);

        const fee = this.calculateFee(totalAmount, configuration).times(conversionRate).decimalPlaces(decimals);

        if (configuration.type === WithdrawalFeeType.Flat)
            configuration.amount = configuration.amount.times(conversionRate).decimalPlaces(decimals);

        if (configuration.type === WithdrawalFeeType.Percentage)
            configuration.maxAmount = configuration.maxAmount?.times(conversionRate).decimalPlaces(decimals);

        return {
            amount: fee,
            configuration
        };
    }

    private buildOutcome(data: Partial<WithdrawalAvailabilityModel>): WithdrawalAvailabilityModel {
        if (!data.currency)
            throw new WithdrawalNotPermittedError('No currency supplied.');

        if (!data.availableAmount)
            data.availableAmount = new BigNumber(0);

        if (!data.netAmount)
            data.netAmount = new BigNumber(0);

        return data as WithdrawalAvailabilityModel;
    }

    private calculateNetAmount(requestedAmount: BigNumber, fee?: WithdrawalFeeModel): BigNumber {
        if (!fee)
            return requestedAmount;

        const amount = requestedAmount.minus(fee.amount);

        if (amount.isLessThan(0))
            return new BigNumber(0);

        return amount;
    }

    private calculateFee(amount: BigNumber, configuration?: WithdrawalFeeConfiguration): BigNumber {
        if (!configuration)
            return new BigNumber(0);

        switch (configuration.type) {
            case WithdrawalFeeType.Flat:
                return configuration.amount;

            case WithdrawalFeeType.Percentage: {
                const fee = new BigNumber(configuration.percentage).dividedBy(100).times(amount);

                if (configuration.maxAmount && fee.isGreaterThan(configuration.maxAmount))
                    return configuration.maxAmount;

                return fee;
            }
        }
    }

    private async getDepositCount(userId: number, currencyCode: string, context: FundsContext, contextOnly: boolean): Promise<number> {
        const statistics = await this.paymenStatisticsManager.getAll(userId);
        const contextStats = contextOnly ? _(statistics).filter(s => s.fundsContext === context) : _(statistics).filter(s => s.fundsContext === context && isEqualIgnoreCase(s.currencyCode, currencyCode));
        return contextStats.sumBy(s => s.depositCount) ?? 0;
    }
}