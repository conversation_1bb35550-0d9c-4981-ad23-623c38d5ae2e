import { <PERSON>ton, Inject } from '../core/ioc';
import Logger, { LogClass } from '../core/logging';
import { DB } from '../core/db';
import { BadRequestError, NotFoundError, PagedResult, BigNumber, ForbiddenError, Config } from '../core';
import { Ledger } from '../banking/ledger';
import { User, UserManager, UserReviewReason, UserTagManager, UserVerificationStatus } from '../user';
import { RequesterType } from '../core/requester-type';
import { TransactionPurpose } from '../banking/transaction-purpose';
import { PlatformEventDispatcher } from '../core/events';
import { UserWalletAccounts } from '../banking/user-wallet-accounts';
import { WalletAccountManager } from '../banking/wallet-account-manager';
import { Withdrawal } from './withdrawal';
import { NewWithdrawal } from './new-withdrawal';
import { WithdrawalProcessorData } from './withdrawal-processor-data';
import { WithdrawalCountFilter, With<PERSON><PERSON><PERSON>ilter, WithdrawalTotalAmountFilter } from './withdrawal-filter';
import { WithdrawalStatus } from './withdrawal-status';
import { WithdrawalRepository } from './repositories';
import { WithdrawalExportResult } from './withdrawal-export-result';
import { WithdrawalGatewayFactory } from './providers';
import { WithdrawalPlatformBalanceManager } from './withdrawal-platform-balance-manager';
import { WithdrawalExporterFactory } from './withdrawal-exporter.factory';
import { WithdrawalStatusChangeResult } from './withdrawal-status-change-result';
import { WithdrawalAddedEvent, WithdrawalConfirmationRequiredEvent, WithdrawalStatusChangedEvent, WithdrawalRequiresAuthorizationEvent, WithdrawalErrorEvent } from './events';
import { PendingWithdrawalCache, WithdrawalCache } from './cache';
import { PaymentConfigurationManager, PaymentManager, PaymentMethodManager, PaymentProvider, PaymentProviderWalletMapper, PaymentStatus, PaymentType } from '../payment';
import { CurrencyConverter, CurrencyManager, CurrencyType, FundsContext, PlatformWallets, UserWalletAccountResolver, WalletEntry } from '../banking';
import { WithdrawalConfigurationRetriever } from './withdrawal-configuration-retriever';
import { Websocket } from '../websocket';
import { WithdrawalPlatformBalance } from './withdrawal-platform-balance';
import { WithdrawalMetadata } from './withdrawal-metadata';
import { WithdrawalLifetimeData, WithdrawalLifetimeDataFilter } from './withdrawal-lifetime';
import { BlockchainPaymentConfigResolver } from '../payment/blockchain-payment-config-resolver';
import { WithdrawalUserTotal } from './withdrawal-user-total';
import { WithdrawalLimitLifetimeDeterminer } from './withdrawal-limit-lifetime-determiner';
import { WITHDRAWAL_CUSTODIAL_KYC_THRESHOLD_BASE, WITHDRAWAL_CUSTODIAL_VERIFICATION_REQUIRED_TAG, WITHDRAWAL_LIMIT_LIFETIME_REVIEW_TAG, WITHDRAWAL_NET_POSITION_REVIEW_TAG } from './constants';
import moment from 'moment';
import { WithdrawalNetPositionDeterminer } from './withdrawal-net-position-determiner';

@Singleton
@LogClass()
export class WithdrawalManager {
    constructor(
        @Inject private readonly cache: WithdrawalCache,
        @Inject private readonly pendingCache: PendingWithdrawalCache,
        @Inject private readonly db: DB,
        @Inject private readonly repository: WithdrawalRepository,
        @Inject private readonly userManager: UserManager,
        @Inject private readonly paymentMethodManager: PaymentMethodManager,
        @Inject private readonly paymentManager: PaymentManager,
        @Inject private readonly withdrawalGatewayFactory: WithdrawalGatewayFactory,
        @Inject private readonly accountManager: WalletAccountManager,
        @Inject private readonly paymentProviderWalletMapper: PaymentProviderWalletMapper,
        @Inject private readonly ledger: Ledger,
        @Inject private readonly eventDispatcher: PlatformEventDispatcher,
        @Inject private readonly withdrawalExporterFactory: WithdrawalExporterFactory,
        @Inject private readonly configurationRetriever: WithdrawalConfigurationRetriever,
        @Inject private readonly websocket: Websocket,
        @Inject private readonly currencyConfigManager: PaymentConfigurationManager,
        @Inject private readonly currencyManager: CurrencyManager,
        @Inject private readonly platformBalanceManager: WithdrawalPlatformBalanceManager,
        @Inject private readonly accountResolver: UserWalletAccountResolver,
        @Inject private readonly paymentConfigResolver: BlockchainPaymentConfigResolver,
        @Inject private readonly userTagManager: UserTagManager,
        @Inject private readonly currencyConverter: CurrencyConverter,
        @Inject private readonly limitLifetimeDeterminer: WithdrawalLimitLifetimeDeterminer,
        @Inject private readonly netPositionDeterminer: WithdrawalNetPositionDeterminer) {
    }

    public async getAll(filter?: WithdrawalFilter): Promise<PagedResult<Withdrawal>> {
        return this.repository.getAll(filter);
    }

    public async getCount(filter?: WithdrawalCountFilter): Promise<number> {
        return this.repository.getCount(filter);
    }

    public async getManyByUser(userId: number, statuses?: WithdrawalStatus[]): Promise<Withdrawal[]> {
        return this.repository.getManyByUser(userId, statuses);
    }

    public async get(id: number): Promise<Withdrawal | undefined> {
        return this.repository.get(id);
    }

    public async getForUser(id: number, userId: number): Promise<Withdrawal | undefined> {
        return this.repository.getForUser(id, userId);
    }

    public async getMany(...ids: number[]): Promise<Withdrawal[]> {
        return this.repository.getMany(...ids);
    }

    public async getProcessable(): Promise<Withdrawal[]> {
        return this.repository.getProcessable();
    }

    public async add(params: NewWithdrawal): Promise<Withdrawal> {
        const user = await this.userManager.get(params.userId);

        if (!user)
            throw new NotFoundError('User not found.');

        if (await this.cache.hasAddPending(user.id))
            throw new ForbiddenError('Unable to add withdrawal at this time.');

        const currency = await this.currencyManager.get(params.currencyCode);

        if (!currency?.active)
            throw new NotFoundError(`Currency ${params.currencyCode} not found.`);

        if (currency.virtual)
            throw new BadRequestError('Withdrawals not supported for virtual currencies.');

        if (!params.fundsContext)
            params.fundsContext = FundsContext.Default;

        if (currency.type === CurrencyType.Crypto && currency.config) {
            if (!params.providerNetwork)
                params.providerNetwork = currency.config.defaultNetwork;

            if (!Object.keys(currency.config?.networks).includes(params.providerNetwork))
                throw new ForbiddenError('Invalid network for currency.');
        }

        let totalAmount = params.amount;

        if (params.fee)
            totalAmount = totalAmount.plus(params.fee);

        if (totalAmount.isLessThanOrEqualTo(0))
            throw new BadRequestError('Invalid withdrawal amount.');

        await this.cache.setAddPending(user.id);

        if (!params.providerNetwork)
            throw new BadRequestError('Provider network not supplied.');

        const paymentProvider = this.currencyConfigManager.getProvider(params.providerNetwork, params.fundsContext);

        if (!params.paymentMethod && params.fundsContext === FundsContext.Default) {
            const paymentConfig = await this.paymentConfigResolver.resolve(user, currency, params.providerNetwork);

            if (paymentProvider !== paymentConfig.paymentProvider)
                throw new BadRequestError(`Payment provider for user ${params.userId} not supplied.`);

            params.paymentMethod = {
                type: paymentConfig.paymentMethod,
                providerRef: paymentConfig.providerRef
            };
        }

        if (!params.paymentMethod)
            throw new BadRequestError('Payment method not supplied.');

        const source = await this.accountResolver.resolve(params.currencyCode, user, params.fundsContext);
        const account = await this.accountManager.getForUser(params.userId, source.account, params.currencyCode);

        if (!account)
            throw new BadRequestError('Withdrawal account not found.');

        if (account.balance.minus(totalAmount).isNegative())
            throw new BadRequestError('Not enough funds available.');

        const paymentMethod = await this.paymentMethodManager.getOrAdd(
            params.paymentMethod.type,
            paymentProvider,
            PaymentType.Withdrawal,
            params.paymentMethod.providerRef,
            params.userId
        );

        if (!paymentMethod)
            throw new NotFoundError(`Payment method for user ${params.userId} not found.`);

        const gateway = this.withdrawalGatewayFactory.create(paymentMethod.provider);
        const requesterType = params.employeeId ? RequesterType.Employee : RequesterType.User;
        const requesterId = params.employeeId || params.userId;
        const [userConfirmable, userCancellable] = await Promise.all([
            this.configurationRetriever.getUserConfirmable(params.currencyCode, source.context, user),
            this.configurationRetriever.getUserCancellable(params.currencyCode, source.context, user)
        ]);

        if (userConfirmable && !params.employeeId) {
            const profile = await this.userManager.getProfile(user.id);

            if (!profile?.email)
                throw new ForbiddenError('Email address required.');
        }

        const withdrawal = await gateway.create(user, paymentMethod, params);
        withdrawal.requesterType = requesterType;
        withdrawal.requesterId = requesterId.toString();
        withdrawal.fee = params.fee ?? new BigNumber(0);
        withdrawal.totalAmount = totalAmount;
        withdrawal.fundsContext = source.context;
        withdrawal.tag = params.tag;
        withdrawal.metadata = {
            ...withdrawal.metadata,
            userConfirmable,
            userCancellable,
        };

        if (withdrawal.amount.isLessThanOrEqualTo(0) || withdrawal.totalAmount.isNaN())
            throw new BadRequestError('Invalid withdrawal amount.');

        const created = await this.repository.add(withdrawal);

        try {
            const result = await this.ledger
                .transfer(withdrawal.totalAmount, params.currencyCode)
                .purpose(TransactionPurpose.Withdrawal)
                .requestedBy(requesterType, requesterId)
                .memo(`Withdrawal ${created.id} for User ${params.userId}`)
                .fromUser(params.userId, source.account)
                .toUser(params.userId, UserWalletAccounts.Escrow)
                .commit();

            await this.repository.addWalletEntry(created.id, result.entry.id);
            await this.eventDispatcher.send(new WithdrawalAddedEvent(created.id));
        } catch (err) {
            Logger.error(`Failed to transfer funds for withdrawal ${created.id}`, err);

            await this.repository.setStatus(created.id, WithdrawalStatus.Error);
            await this.eventDispatcher.send(new WithdrawalErrorEvent(created.id, err.message));

            throw err;
        }

        if (userConfirmable && !params.employeeId) {
            await this.eventDispatcher.send(new WithdrawalConfirmationRequiredEvent(created.id, user.id));
            await this.websocket.user(withdrawal.userId)
                .type('Withdrawal:StatusChange')
                .payload({
                    withdrawalId: withdrawal.id,
                    status: withdrawal.status,
                    provider: withdrawal.provider,
                    currencyCode: withdrawal.currencyCode,
                    providerRef: withdrawal.providerRef,
                    targetCompletionTime: withdrawal.targetCompletionTime,
                    fundsContext: withdrawal.fundsContext
                })
                .send();

            return created;
        }

        await this.confirm(created);
        return await this.repository.primary(r => r.get(created.id)) as Withdrawal;
    }

    public async confirm(withdrawal: Withdrawal): Promise<void> {
        if (withdrawal.status !== WithdrawalStatus.Pending)
            throw new BadRequestError('Withdrawal not in pending state.');

        const user = await this.userManager.get(withdrawal.userId);

        if (!user)
            throw new NotFoundError('User not found.');

        await this.pendingCache.deleteById(withdrawal.id);

        if (await this.checkLifetimeLimits(user, withdrawal.currencyCode, withdrawal.fundsContext)) {
            // Withdrawal should be marked as confirmed until player account is reviewed.
            // When review is complete, remove tag and manually approve withdrawal.
            await this.setReviewRequired(user, WITHDRAWAL_LIMIT_LIFETIME_REVIEW_TAG, UserReviewReason.WithdrawalLimits);
            await this.setMetadata(withdrawal, { lifetimeReview: true, userCancellable: false }, true);
            await this.confirmed(withdrawal);
            return;
        }

        if (await this.checkNetPosition(user, withdrawal.currencyCode, withdrawal.fundsContext)) {
            await this.setReviewRequired(user, WITHDRAWAL_NET_POSITION_REVIEW_TAG, UserReviewReason.NetPositionThreshold);
            await this.setMetadata(withdrawal, { netPositionReview: true, userCancellable: false }, true);
            await this.confirmed(withdrawal);
            return;
        }

        if (await this.checkKYCThreshold(withdrawal.userId, withdrawal.amount, withdrawal.currencyCode, withdrawal.fundsContext)) {
            await this.setReviewRequired(user, WITHDRAWAL_CUSTODIAL_VERIFICATION_REQUIRED_TAG, UserReviewReason.WithdrawalKYCThresholdBreached);
            await this.setMetadata(withdrawal, { kycThresholdReached: true, userCancellable: false }, true);
            await this.confirmed(withdrawal);
            return;
        }

        if (await this.assessRiskThresholds(withdrawal)) {
            await this.userVerificationRequired(withdrawal);
            return;
        }

        if (await this.satisfiesAutoApproval(withdrawal)) {
            await this.approve(withdrawal.id, RequesterType.System);
            return;
        }

        await this.confirmed(withdrawal);
    }

    public async confirmByToken(token: string): Promise<void> {
        const cached = await this.pendingCache.get(token);

        if (!cached)
            throw new ForbiddenError('Withdrawal expired or already updated.');

        const withdrawal = await this.repository.get(cached.id);

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        await this.cache.lock(withdrawal.id, async () => {
            const confirmable = await this.configurationRetriever.getUserConfirmable(
                withdrawal.currencyCode,
                withdrawal.fundsContext
            );

            if (!confirmable)
                throw new BadRequestError('Withdrawal not confirmable.');

            await this.pendingCache.delete(token);
            await this.confirm(withdrawal);
        });
    }

    public async cancelByToken(token: string): Promise<void> {
        const cached = await this.pendingCache.get(token);

        if (!cached)
            throw new ForbiddenError('Withdrawal expired or already updated.');

        const withdrawal = await this.repository.get(cached.id);

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        await this.cache.lock(withdrawal.id, async () => {
            const isConfigCancellable = await this.configurationRetriever.getUserCancellable(
                withdrawal.currencyCode,
                withdrawal.fundsContext
            );

            const isUserCancellable = withdrawal.metadata?.userCancellable || true;

            if (!isConfigCancellable || !isUserCancellable)
                throw new BadRequestError('Withdrawal cannot be cancelled.');

            const user = await this.userManager.get(withdrawal.userId);

            if (!user)
                throw new NotFoundError('User not found.');

            if (user.tags?.includes(WITHDRAWAL_LIMIT_LIFETIME_REVIEW_TAG) || user.tags?.includes(WITHDRAWAL_NET_POSITION_REVIEW_TAG))
                throw new BadRequestError('Withdrawal cannot be cancelled.');

            await this.pendingCache.delete(token);
            await this.cancel(withdrawal);
        });
    }

    public async approve(idOrRef: number | Withdrawal, employeeId?: string): Promise<void> {
        const withdrawal = typeof idOrRef === 'number'
            ? await this.repository.get(idOrRef)
            : idOrRef;

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        const user = await this.userManager.get(withdrawal.userId);

        if (!user)
            throw new NotFoundError('User not found.');

        if (user.tags?.includes(`Withdrawal:${withdrawal.fundsContext}:UserVerificationRequired`))
            throw new BadRequestError('User requires verification before withdrawal can be approved.');

        if (user.tags?.includes(WITHDRAWAL_LIMIT_LIFETIME_REVIEW_TAG) || user.tags?.includes(WITHDRAWAL_NET_POSITION_REVIEW_TAG))
            throw new BadRequestError('User requires review before withdrawal can be approved.');

        if (![WithdrawalStatus.Pending, WithdrawalStatus.Confirmed, WithdrawalStatus.Error].includes(withdrawal.status))
            throw new BadRequestError('Withdrawal not in pending or confirmed state.');

        const currencyConfiguration = await this.configurationRetriever.retrieve(
            withdrawal.currencyCode,
            withdrawal.fundsContext,
            user
        );

        if (!currencyConfiguration)
            throw new NotFoundError(`${withdrawal.currencyCode} withdrawal configuration not found.`);

        await this.pendingCache.deleteById(withdrawal.id);

        await this.repository.setStatus(withdrawal.id, WithdrawalStatus.Approved);

        withdrawal.targetCompletionTime = moment().utc().add(currencyConfiguration.targetDays, 'days').toDate();
        await this.repository.setTargetCompletionTime(withdrawal.id, withdrawal.targetCompletionTime);

        if (employeeId)
            await this.repository.setApprovedBy(withdrawal.id, employeeId);

        await this.sendStatusChangeEvents(withdrawal, WithdrawalStatus.Approved);
    }

    public async process(withdrawal: Withdrawal, employeeId?: string, data?: WithdrawalProcessorData): Promise<void> {
        if (![WithdrawalStatus.Approved, WithdrawalStatus.InsufficientFunds].includes(withdrawal.status))
            throw new BadRequestError('Withdrawal not in approved state.');

        const gateway = this.withdrawalGatewayFactory.create(withdrawal.provider);
        const approvedBy = employeeId || RequesterType.System;
        const status = await gateway.process(withdrawal, data);

        if (status === withdrawal.status)
            return;

        await this.repository.setStatus(withdrawal.id, status);
        await this.repository.setApprovedBy(withdrawal.id, approvedBy);

        withdrawal = await this.get(withdrawal.id) as Withdrawal;

        await this.sendStatusChangeEvents(withdrawal, status);

        if (status === WithdrawalStatus.Error)
            throw new Error('There was an error processing the withdrawal request, it is now in an errored state.');
    }

    public async complete(id: number, employeeId?: string, data?: WithdrawalProcessorData): Promise<void> {
        const withdrawal = await this.repository.get(id);

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        if (![WithdrawalStatus.Processing, WithdrawalStatus.RequiresAuthorization, WithdrawalStatus.Error].includes(withdrawal.status))
            throw new BadRequestError('Withdrawal not in processing or requires authorization state.');

        const gateway = this.withdrawalGatewayFactory.create(withdrawal.provider);

        const metadata = withdrawal.metadata ?? {};
        metadata.completionAttempts = metadata.completionAttempts ? metadata.completionAttempts + 1 : 1;
        withdrawal.metadata = metadata;

        await this.repository.setMetadata(id, metadata);

        let status = await gateway.complete(withdrawal, data);

        if (status === withdrawal.status)
            return;

        if (status === WithdrawalStatus.Complete)
            try {
                const requesterType = employeeId ? RequesterType.Employee : RequesterType.System;
                const requesterId = employeeId || withdrawal.provider;
                const destinationWallet = this.paymentProviderWalletMapper.map(withdrawal.provider);

                const walletEntries = await this.getWalletEntries(id);
                const withdrawalEntry = walletEntries.find(
                    e => e.purpose === TransactionPurpose.Withdrawal &&
                        e.requesterType === RequesterType.System &&
                        e.requesterId === withdrawal.provider);

                if (!withdrawalEntry) {
                    const result = await this.ledger
                        .transfer(withdrawal.amount, withdrawal.currencyCode)
                        .purpose(TransactionPurpose.Withdrawal)
                        .requestedBy(requesterType, requesterId)
                        .memo(`Withdrawal completed`)
                        .fromUser(withdrawal.userId, UserWalletAccounts.Escrow)
                        .toPlatform(destinationWallet)
                        .commit();

                    await this.repository.addWalletEntry(id, result.entry.id);
                }

                if (!withdrawal.paymentId) {
                    const payment = await this.paymentManager.add({
                        userId: withdrawal.userId,
                        amount: withdrawal.amount,
                        currencyCode: withdrawal.currencyCode,
                        provider: withdrawal.provider,
                        providerRef: withdrawal.providerRef || 'N/A',
                        providerNetwork: withdrawal.providerNetwork,
                        paymentMethodId: withdrawal.paymentMethodId,
                        status: PaymentStatus.Successful,
                        type: PaymentType.Withdrawal,
                        origin: withdrawal.origin,
                        fundsContext: withdrawal.fundsContext,
                        metadata: withdrawal.metadata ?? undefined,
                        processorRef: withdrawal.metadata?.fireblocksTxId ?? undefined
                    });

                    await this.repository.setPayment(withdrawal.id, payment.id);
                    withdrawal.paymentId = payment.id;
                } else {
                    const payment = await this.paymentManager.get(withdrawal.paymentId);

                    if (payment?.provider === PaymentProvider.Fireblocks) {
                        payment.processorRef = withdrawal.metadata?.fireblocksTxId;

                        await this.paymentManager.update(payment);
                    }
                }

                if (withdrawal.fee.isGreaterThan(0)) {
                    const feeEntry = walletEntries.find(e => e.purpose === TransactionPurpose.WithdrawalFee);

                    if (!feeEntry) {
                        const feeResult = await this.ledger
                            .transfer(withdrawal.fee, withdrawal.currencyCode)
                            .purpose(TransactionPurpose.WithdrawalFee)
                            .requestedBy(RequesterType.System, 'Withdrawal')
                            .memo(`Withdrawal fee for withdrawal ${withdrawal.id}`)
                            .fromUser(withdrawal.userId, UserWalletAccounts.Escrow)
                            .toPlatform(PlatformWallets.Corporate)
                            .commit();

                        await this.repository.addWalletEntry(id, feeResult.entry.id);
                    }
                }
            } catch (err) {
                Logger.error(`Error performing wallet transfer for withdrawal ${withdrawal.id}`, err);
                status = WithdrawalStatus.Error;
            }

        await this.repository.setStatus(id, status);
        await this.sendStatusChangeEvents(withdrawal, status);

        if (status === WithdrawalStatus.Error) {
            await this.eventDispatcher.send(new WithdrawalErrorEvent(withdrawal.id));

            throw new Error('There was an error processing the withdrawal request, it is now in an errored state.');
        }
    }

    public async revert(id: number, employeeId?: string): Promise<void> {
        const withdrawal = await this.repository.get(id);

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        if (withdrawal.status !== WithdrawalStatus.Complete)
            throw new BadRequestError('Withdrawal is not complete.');

        if (!withdrawal.manualProcessing)
            throw new BadRequestError('Withdrawal was not manually processed.');

        const requesterType = employeeId ? RequesterType.Employee : RequesterType.System;
        const requesterId = employeeId || withdrawal.provider;
        const sourceWallet = this.paymentProviderWalletMapper.map(withdrawal.provider);

        const result = await this.ledger
            .transfer(withdrawal.amount, withdrawal.currencyCode)
            .purpose(TransactionPurpose.Withdrawal)
            .requestedBy(requesterType, requesterId)
            .memo(`Withdrawal reverted`)
            .fromPlatform(sourceWallet)
            .toUser(withdrawal.userId, UserWalletAccounts.Escrow)
            .commit();

        await this.repository.addWalletEntry(id, result.entry.id);

        if (withdrawal.fee.isGreaterThan(0)) {
            const feeResult = await this.ledger
                .transfer(withdrawal.fee, withdrawal.currencyCode)
                .purpose(TransactionPurpose.WithdrawalFee)
                .requestedBy(RequesterType.System, 'Withdrawal')
                .memo(`Withdrawal fee for reverted withdrawal ${withdrawal.id}`)
                .fromPlatform(PlatformWallets.Corporate)
                .toUser(withdrawal.userId, UserWalletAccounts.Escrow)
                .commit();

            await this.repository.addWalletEntry(id, feeResult.entry.id);
        }

        await this.repository.setStatus(id, WithdrawalStatus.Confirmed);

        if (employeeId)
            await this.repository.setApprovedBy(id, employeeId);

        await this.sendStatusChangeEvents(withdrawal, WithdrawalStatus.Approved);
    }

    public async cancelForUser(id: number, userOrId: User | number): Promise<void> {
        await this.cache.lock(id, async () => {
            const user = typeof userOrId === 'number' ? await this.userManager.get(userOrId) : userOrId;

            if (!user)
                throw new NotFoundError('User not found.');

            const withdrawal = await this.getForUser(id, user.id);

            if (!withdrawal)
                throw new NotFoundError('Withdrawal could not be found.');

            const isConfigCancellable = await this.configurationRetriever.getUserCancellable(
                withdrawal.currencyCode,
                withdrawal.fundsContext
            );

            const isUserCancellable = withdrawal.metadata?.userCancellable ?? true;
            const isLifetimeLimitReview = user.tags?.includes(WITHDRAWAL_LIMIT_LIFETIME_REVIEW_TAG);
            const isNetPositionReview = user.tags?.includes(WITHDRAWAL_NET_POSITION_REVIEW_TAG);

            if (!isConfigCancellable || !isUserCancellable || isLifetimeLimitReview || isNetPositionReview)
                throw new ForbiddenError('Withdrawal cannot be cancelled by user.');

            await this.cancel(id);
        });
    }

    public async cancel(idOrRef: number | Withdrawal): Promise<void> {
        const withdrawal = typeof idOrRef === 'number'
            ? await this.repository.get(idOrRef)
            : idOrRef;

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        if (![WithdrawalStatus.Pending, WithdrawalStatus.Approved, WithdrawalStatus.Confirmed].includes(withdrawal.status))
            throw new BadRequestError('Withdrawal is not pending, confirmed or approved.');

        if (withdrawal.targetCompletionTime && moment.utc().isAfter(withdrawal.targetCompletionTime))
            throw new BadRequestError('Withdrawal cannot be cancelled beyond its target completion time.');

        await this.pendingCache.deleteById(withdrawal.id);

        const gateway = this.withdrawalGatewayFactory.create(withdrawal.provider);
        const status = await gateway.cancel(withdrawal);

        if (status === WithdrawalStatus.Cancelled) {
            const requesterType = RequesterType.User;
            const requesterId = withdrawal.userId;
            const target = await this.accountResolver.resolve(withdrawal.fundsContext);

            const result = await this.ledger
                .transfer(withdrawal.totalAmount, withdrawal.currencyCode)
                .purpose(TransactionPurpose.Withdrawal)
                .requestedBy(requesterType, requesterId)
                .memo(`Withdrawal cancelled`)
                .fromUser(withdrawal.userId, UserWalletAccounts.Escrow)
                .toUser(withdrawal.userId, target.account)
                .commit();

            await this.repository.addWalletEntry(withdrawal.id, result.entry.id);
        }

        if (status === withdrawal.status)
            return;

        await this.repository.setStatus(withdrawal.id, status);
        await this.sendStatusChangeEvents(withdrawal, status);
    }

    public async decline(idOrRef: number | Withdrawal, employeeId: string, memo?: string): Promise<void> {
        const withdrawal = typeof idOrRef === 'number'
            ? await this.repository.get(idOrRef)
            : idOrRef;

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        const ALLOWED_STATES: WithdrawalStatus[] = [
            WithdrawalStatus.Pending,
            WithdrawalStatus.Approved,
            WithdrawalStatus.Confirmed,
            WithdrawalStatus.Error,
            WithdrawalStatus.RequiresAuthorization,
            WithdrawalStatus.InsufficientFunds
        ];

        if (!ALLOWED_STATES.includes(withdrawal.status))
            throw new BadRequestError('Withdrawal is not pending, approved, confirmed, errored, requires authorization or insufficient funds.');

        const gateway = this.withdrawalGatewayFactory.create(withdrawal.provider);
        const status = await gateway.decline(withdrawal, memo);

        if (status === WithdrawalStatus.Declined) {
            const escrowAccount = await this.accountManager.getForUser(withdrawal.userId, UserWalletAccounts.Escrow, withdrawal.currencyCode);

            if (!escrowAccount || escrowAccount.balance.isLessThan(withdrawal.totalAmount)) {
                Logger.warn(`Insufficient funds in escrow for withdrawal ${withdrawal.id}. Skipping escrow transfer.`);
                const updatedMetadata = {
                    ...withdrawal.metadata,
                    escrowTransferSkipped: {
                        reason: 'Insufficient funds in escrow',
                        time: new Date().toISOString()
                    }
                };
                await this.repository.setMetadata(withdrawal.id, updatedMetadata);
            } else {
                const requesterType = RequesterType.Employee;
                const requesterId = employeeId;
                const target = await this.accountResolver.resolve(withdrawal.fundsContext);

                const result = await this.ledger
                    .transfer(withdrawal.totalAmount, withdrawal.currencyCode)
                    .purpose(TransactionPurpose.Withdrawal)
                    .requestedBy(requesterType, requesterId)
                    .memo(`Withdrawal declined${memo ? ': ' + memo : ''}`)
                    .fromUser(withdrawal.userId, UserWalletAccounts.Escrow)
                    .toUser(withdrawal.userId, target.account)
                    .commit();

                await this.repository.addWalletEntry(withdrawal.id, result.entry.id);
            }
        }

        if (status === withdrawal.status)
            return;

        await this.repository.setStatus(withdrawal.id, status);
        await this.sendStatusChangeEvents(withdrawal, status);
    }

    public async fail(id: number, reason?: string): Promise<void> {
        const withdrawal = await this.repository.get(id);

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        if (withdrawal.status === WithdrawalStatus.Error) {
            Logger.info(`Withdrawal ${id} is already in ${WithdrawalStatus.Error} status.`);
            return;
        }

        const newStatus = WithdrawalStatus.Error;
        await this.repository.setStatus(id, newStatus);
        await this.eventDispatcher.send(new WithdrawalErrorEvent(withdrawal.id, reason));
        await this.sendStatusChangeEvents(withdrawal, newStatus);
    }

    public async pendingAuthorisation(id: number): Promise<void> {
        const withdrawal = await this.repository.get(id);

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        if (withdrawal.status === WithdrawalStatus.RequiresAuthorization) {
            Logger.info(`Withdrawal ${id} is already in ${WithdrawalStatus.RequiresAuthorization} status.`);
            return;
        }

        const newStatus = WithdrawalStatus.RequiresAuthorization;
        await this.repository.setStatus(id, newStatus);
        await this.eventDispatcher.send(new WithdrawalRequiresAuthorizationEvent(withdrawal.id, newStatus));
        await this.sendStatusChangeEvents(withdrawal, newStatus);
    }

    public async bulkChangeStatus(status: WithdrawalStatus, ids: number[], employeeId?: string): Promise<WithdrawalStatusChangeResult[]> {
        await this.db.getConnection();

        let changes: Promise<WithdrawalStatusChangeResult>[];

        switch (status) {
            case WithdrawalStatus.Confirmed:
                changes = ids.map((id: number) => this.getStatusChangeResult(id, this.approve(id, employeeId)));
                break;

            case WithdrawalStatus.Complete:
                changes = ids.map((id: number) => this.getStatusChangeResult(id, this.revert(id)));
                break;

            default:
                throw new BadRequestError(`Withdrawal status '${status}' is not supported.`);
        }

        return Promise.all(changes);
    }

    public async export(provider: PaymentProvider, ...ids: number[]): Promise<WithdrawalExportResult> {
        const exporter = this.withdrawalExporterFactory.create(provider);
        const requests = await this.getMany(...ids);
        return exporter.export(requests);
    }

    public async getWalletEntries(id: number): Promise<WalletEntry[]> {
        return this.repository.getWalletEntries(id);
    }

    public async getByProviderRef(provider: PaymentProvider, providerRef: string): Promise<Withdrawal | undefined> {
        return this.repository.getByProviderRef(provider, providerRef);
    }

    public async getTotalAmountForUser(userId: number, filter: WithdrawalTotalAmountFilter): Promise<WithdrawalUserTotal> {
        return this.repository.getTotalAmountForUser(userId, filter);
    }

    public async getPlatformBalance(...currencyCodes: string[]): Promise<WithdrawalPlatformBalance[]> {
        return this.platformBalanceManager.getMany(currencyCodes);
    }

    public async setMetadata(idOrRef: number | Withdrawal, metadata: WithdrawalMetadata, merge?: boolean): Promise<Withdrawal> {
        const withdrawal = typeof idOrRef === 'number'
            ? await this.repository.get(idOrRef)
            : idOrRef;

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        let data = (merge ? {
            ...withdrawal.metadata,
            ...metadata
        } : metadata) as WithdrawalMetadata;

        if (!Object.keys(data).length)
            data = null as unknown as WithdrawalMetadata;

        await this.repository.setMetadata(withdrawal.id, data);
        withdrawal.metadata = data;
        return withdrawal;
    }

    public async getLifetimeTotalsForUser(userId: number, filter?: WithdrawalLifetimeDataFilter): Promise<WithdrawalLifetimeData[]> {
        return this.repository.getLifetimeDataForUser(userId, filter);
    }

    public async getLifetimeTotalBaseAmountForUser(userId: number, filter?: WithdrawalLifetimeDataFilter): Promise<BigNumber> {
        const datas = await this.getLifetimeTotalsForUser(userId, filter);
        return BigNumber.sum(...datas.map(t => t.baseAmount));
    }

    public async getByPaymentId(paymentId: number): Promise<Withdrawal | undefined> {
        return this.repository.getByPaymentId(paymentId);
    }

    public async sendConfirmationRequired(idOrRef: number | Withdrawal): Promise<void> {
        const withdrawal = typeof idOrRef === 'number'
            ? await this.repository.get(idOrRef)
            : idOrRef;

        if (!withdrawal)
            throw new NotFoundError('Withdrawal not found.');

        await this.pendingCache.deleteById(withdrawal.id);

        const isConfigConfirmable = await this.configurationRetriever.getUserConfirmable(
            withdrawal.currencyCode,
            withdrawal.fundsContext
        );

        const isUserConfirmable = withdrawal.metadata?.userConfirmable ?? true;

        if (!isConfigConfirmable || !isUserConfirmable)
            throw new BadRequestError('Withdrawal not confirmable.');

        await this.eventDispatcher.send(new WithdrawalConfirmationRequiredEvent(withdrawal.id, withdrawal.userId));
    }

    private async sendStatusChangeEvents(withdrawal: Withdrawal, newStatus: WithdrawalStatus): Promise<void> {
        await this.eventDispatcher.send(new WithdrawalStatusChangedEvent(withdrawal.id, newStatus));
        await this.websocket.user(withdrawal.userId)
            .type('Withdrawal:StatusChange')
            .payload({
                withdrawalId: withdrawal.id,
                status: newStatus,
                provider: withdrawal.provider,
                currencyCode: withdrawal.currencyCode,
                providerRef: withdrawal.providerRef,
                targetCompletionTime: withdrawal.targetCompletionTime,
                fundsContext: withdrawal.fundsContext
            })
            .send();
    }

    private async getStatusChangeResult(id: number, changeOperation: Promise<void>): Promise<WithdrawalStatusChangeResult> {
        const model: WithdrawalStatusChangeResult = { id, success: false };

        return changeOperation
            .then(() => {
                model.success = true;
                return model;
            })
            .catch((reason: Error) => {
                model.message = reason.message;
                return model;
            });
    }

    private async satisfiesAutoApproval(withdrawal: Withdrawal): Promise<boolean> {
        const user = await this.userManager.get(withdrawal.userId);

        if (!user)
            throw new NotFoundError('User not found.');

        if (user.tags?.some(tag => tag === 'WithdrawalApprovalRequired' || tag === `WithdrawalApprovalRequired-${withdrawal.fundsContext}`))
            return false;

        if (user.tags?.some(tag => tag === 'WithdrawalApprovalSkip' || tag === `WithdrawalApprovalSkip-${withdrawal.fundsContext}`))
            return true;

        const configuration = await this.configurationRetriever.retrieve(
            withdrawal.currencyCode,
            withdrawal.fundsContext,
            user
        );

        const autoApprovalConfiguration = configuration?.autoApproval;
        if (!autoApprovalConfiguration)
            return true;

        let withdrawalAmount = withdrawal.amount;
        if (configuration.currencyCode !== withdrawal.currencyCode) {
            const convertedAmount = await this.currencyConverter.convert(withdrawal.currencyCode, configuration.currencyCode, withdrawal.amount);
            withdrawalAmount = convertedAmount.amountConverted;
        }

        if (autoApprovalConfiguration.limit && !autoApprovalConfiguration.periodInHours)
            return withdrawalAmount.isLessThanOrEqualTo(autoApprovalConfiguration.limit);

        if (autoApprovalConfiguration.periodInHours && autoApprovalConfiguration.periodInHours <= 0)
            return withdrawalAmount.isLessThanOrEqualTo(autoApprovalConfiguration.limit);

        const now = moment.utc().toDate();
        const withdrawalTotal = await this.getTotalAmountForUser(withdrawal.userId, {
            currencyCode: configuration.currencyCode === Config.baseCurrency ? undefined : withdrawal.currencyCode,
            fundsContext: configuration.limitsIgnoreContext ? undefined : withdrawal.fundsContext,
            createdFrom: moment(now).subtract(autoApprovalConfiguration.periodInHours, 'hours').toDate(),
            createdTo: now,
            statuses: [
                WithdrawalStatus.Pending,
                WithdrawalStatus.Confirmed,
                WithdrawalStatus.Approved,
                WithdrawalStatus.Complete,
                WithdrawalStatus.InsufficientFunds,
                WithdrawalStatus.Processing,
                WithdrawalStatus.RequiresAuthorization
            ]
        });

        return withdrawalTotal.amount.isLessThanOrEqualTo(autoApprovalConfiguration.limit);
    }

    private async assessRiskThresholds(withdrawal: Withdrawal): Promise<boolean> {
        const user = await this.userManager.get(withdrawal.userId);

        if (!user)
            throw new NotFoundError('User not found.');

        // Stop processing if the user already has verification tag.
        // Withdrawal state should be 'Confirmed' until admins perform KYC/AML checks on player.
        if (user.tags?.includes(`Withdrawal:${withdrawal.fundsContext}:UserVerificationRequired`))
            return true;

        // If the player has already been KYC/AML checked stop processing risk.
        // Withdrawal should be applicable for automatic approval.
        if (user.identityStatus === UserVerificationStatus.Passed)
            return false;

        const config = await this.configurationRetriever.getRiskConfiguration(withdrawal.fundsContext);

        if (config.standaloneLimit) {
            const withdrawalBaseAmount = await this.getWithdrawalBaseAmount(withdrawal.currencyCode, withdrawal.amount);

            if (withdrawalBaseAmount.isGreaterThanOrEqualTo(config.standaloneLimit))
                return true;
        }

        if (!config.cumulativeLimit)
            return false;

        const lifetimeAmount = await this.getLifetimeTotalBaseAmountForUser(user.id, {
            fundsContext: withdrawal.fundsContext
        });

        return lifetimeAmount.isGreaterThanOrEqualTo(config.cumulativeLimit);
    }

    private async userVerificationRequired(withdrawal: Withdrawal): Promise<void> {
        const user = await this.userManager.get(withdrawal.userId);

        if (!user)
            throw new NotFoundError('User not found.');

        const tagName = `Withdrawal:${withdrawal.fundsContext}:UserVerificationRequired`;

        if (!user.tags?.includes(tagName)) {
            await this.userTagManager.add(user.id, tagName);
            await this.userManager.setVerificationRequired(user.id);
        }

        await this.confirmed(withdrawal);
    }

    private async confirmed(withdrawal: Withdrawal): Promise<void> {
        await this.repository.setStatus(withdrawal.id, WithdrawalStatus.Confirmed);
        await this.repository.setApprovedBy(withdrawal.id, RequesterType.System);
        await this.sendStatusChangeEvents(withdrawal, WithdrawalStatus.Confirmed);
    }

    private async getWithdrawalBaseAmount(currencyCode: string, amount: BigNumber): Promise<BigNumber> {
        const { amountConverted } = await this.currencyConverter.convert(currencyCode, Config.baseCurrency, amount);
        return amountConverted;
    }

    private async checkLifetimeLimits(userIdOrRef: number | User, currencyCode: string, fundsContext: FundsContext): Promise<boolean> {
        const user = typeof userIdOrRef === 'number'
            ? await this.userManager.get(userIdOrRef)
            : userIdOrRef;

        if (!user)
            throw new NotFoundError('User not found.');

        if (user.tags?.includes(WITHDRAWAL_LIMIT_LIFETIME_REVIEW_TAG))
            return true;

        const limits = await this.limitLifetimeDeterminer.determine(user, currencyCode, fundsContext);

        if (!limits.lifetimeLimitRemaining)
            return false;

        return limits.lifetimeLimitRemaining.isZero();
    }

    private async checkNetPosition(userIdOrRef: number | User, currencyCode: string, fundsContext: FundsContext): Promise<boolean> {
        const user = typeof userIdOrRef === 'number'
        ? await this.userManager.get(userIdOrRef)
        : userIdOrRef;

        if (!user)
            throw new NotFoundError('User not found.');

        if (user.tags?.includes(WITHDRAWAL_NET_POSITION_REVIEW_TAG))
            return true;

        const outcome = await this.netPositionDeterminer.determine(user, currencyCode, fundsContext);
        if (!outcome)
            return false;

        return outcome.position.isLessThan(outcome.threshold);
    }

    private async checkKYCThreshold(userIdOrRef: number | User, amount: BigNumber, currencyCode: string, fundsContext: FundsContext): Promise<boolean> {
        if (fundsContext !== FundsContext.Custodial)
            return false;

        const user = typeof userIdOrRef === 'number'
        ? await this.userManager.get(userIdOrRef)
        : userIdOrRef;

        if (!user)
            throw new NotFoundError('User not found.');

        if (user.identityStatus === UserVerificationStatus.Passed)
            return false;

        const { amountConverted } = await this.currencyConverter.convert(currencyCode, Config.baseCurrency, amount);
        return amountConverted.isGreaterThanOrEqualTo(WITHDRAWAL_CUSTODIAL_KYC_THRESHOLD_BASE);
    }

    private async setReviewRequired(userIdOrRef: number | User, tag: string, reason: UserReviewReason): Promise<void> {
        const user = typeof userIdOrRef === 'number'
            ? await this.userManager.get(userIdOrRef)
            : userIdOrRef;

        if (!user)
            throw new NotFoundError('User not found.');

        if (user.tags?.includes(tag))
            return;

        await this.userTagManager.add(user.id, tag);
        await this.userManager.setReviewRequired(user.id, reason);
    }
}