import { Singleton, Inject } from '../core/ioc';
import Logger, { LogClass } from '../core/logging';
import { BadRequestError, BigNumber, ConflictError, NotFoundError } from '../core';
import { Websocket } from '../websocket';
import { SweepstakeGameBuyInBalance } from './sweepstake-game-buyin';
import { SweepstakeGameBuyInBalanceCache } from './cache';
import { SweepstakeGameBuyInBalanceModelMapper } from './models/mappers';
import { PlatformSweepstake, Sweepstake } from './sweepstake';
import { SweepstakeType } from './sweepstake-type';
import { SweepstakeManager } from './sweepstake-manager';
import { ActivePlatformSweepstake } from './sweepstake-active';
import { SweepstakeEntrySetManager } from './sweepstake-entry-set-manager';
import { SweepstakeEntrySet } from './sweepstake-entry-set';
import { SweepstakeEntryState } from './sweepstake-entry-state';
import { SweepstakeGameBuyInBalanceRepository } from './repositories';
import { SweepstakeGameBuyInBalanceResetMode } from './sweepstake-game-buyin-balance-reset-mode';
import { isActiveState } from './utilities';

enum AmountOperation {
    Add,
    Deduct
}

interface CurrentBalance {
    balance: SweepstakeGameBuyInBalance;
    create: boolean;
}

interface ValidSweepstakeEntry {
    sweepstake: PlatformSweepstake;
    entry: SweepstakeEntrySet;
}

interface SweepstakeTargetMultiplier {
    target: BigNumber;
    multiplier: number;
}

interface AfterDeductionResult {
    validatedBalance: SweepstakeGameBuyInBalance;
    create: boolean;
}

@Singleton
@LogClass()
export class SweepstakeGameBuyInBalanceManager {
    constructor(
        @Inject private readonly sweepstakeManager: SweepstakeManager,
        @Inject private readonly entrySetManager: SweepstakeEntrySetManager,
        @Inject private readonly buyInBalanceCache: SweepstakeGameBuyInBalanceCache,
        @Inject private readonly buyInBalanceMapper: SweepstakeGameBuyInBalanceModelMapper,
        @Inject private readonly websocket: Websocket,
        @Inject private readonly repository: SweepstakeGameBuyInBalanceRepository) {
    }

    public async getAll(idOrRef: number | Sweepstake): Promise<SweepstakeGameBuyInBalance[]> {
        const sweepstake = await this.getValidSweepstake(idOrRef);

        if (!sweepstake)
            throw new BadRequestError(`Sweepstake ${idOrRef} is not a valid game buy-in entry sweepstake.`);

        const cached = await this.buyInBalanceCache.getAll(sweepstake.id);

        if (cached.length > 0)
            return cached;

        const balances = await this.repository.getAll(sweepstake.id);

        if (balances.length === 0)
            return [];

        if (isActiveState(sweepstake))
            await this.buyInBalanceCache.store(sweepstake.closeTime, ...balances);

        return balances;
    }

    public async get(idOrRef: number | Sweepstake, userId: number): Promise<SweepstakeGameBuyInBalance | undefined> {
        const sweepstake = await this.getValidSweepstake(idOrRef);

        if (!sweepstake)
            throw new BadRequestError(`Sweepstake ${idOrRef} is not a valid game buy-in entry sweepstake.`);

        const cached = await this.buyInBalanceCache.get(sweepstake.id, userId);

        if (cached)
            return cached;

        const balance = await this.repository.get(sweepstake.id, userId);

        if (!balance)
            return;

        if (isActiveState(sweepstake))
            await this.buyInBalanceCache.store(sweepstake.closeTime, balance);

        return balance;
    }

    public async add(balance: SweepstakeGameBuyInBalance, cacheExpiryTime?: Date): Promise<void> {
        const existing = await this.repository.get(balance.sweepstakeId, balance.userId);

        if (existing)
            throw new ConflictError(`Buy-in balance for user ${balance.userId} already exists for sweepstake ${balance.sweepstakeId}.`);

        const added = await this.repository.add(balance);
        await this.buyInBalanceCache.store(cacheExpiryTime, added);
    }

    public async update(balance: SweepstakeGameBuyInBalance): Promise<void> {
        const existing = await this.repository.get(balance.sweepstakeId, balance.userId);

        if (!existing)
            throw new NotFoundError(`Buy-in balance for user ${balance.userId} not found for sweepstake ${balance.sweepstakeId}.`);

        const updated = await this.repository.update(balance);
        await this.buyInBalanceCache.update(updated);
    }

    public async upsert(balance: SweepstakeGameBuyInBalance, cacheExpiryTime?: Date, create: boolean = false): Promise<void> {
        await this.repository.upsert(balance, create);

        if (create)
            await this.buyInBalanceCache.store(cacheExpiryTime, balance);
        else
            await this.buyInBalanceCache.update(balance);
    }

    public async addEntry(entryIdOrRef: number | SweepstakeEntrySet): Promise<void> {
        const result = await this.validateSweepstakeAndEntry(entryIdOrRef);
        if (!result)
            return;

        const { entry, sweepstake } = result;
        const userId = entry.user.id;

        return this.buyInBalanceCache.lock(sweepstake.id, userId, async () => {
            const entryCount = entry.count;
            const { balance, create } = await this.getBalance(sweepstake, userId);

            // Add the specified entry count to the manualEntryAdditions tracker.
            // This keeps manual entries separate , ensuring entryCount is
            // accurately used for calculating the next target based on system-driven entries.
            balance.manualEntryAdditions += entryCount;

            const validatedBalance = this.getValidatedBalance(balance, sweepstake, userId);

            await this.saveBalanceAndNotify(validatedBalance, sweepstake, create);

            Logger.info(`Updated balance for user ${userId} in sweepstake ${sweepstake.id} after adding ${entryCount} manual entries.`);
        });
    }

    public async removeEntry(entryIdOrRef: number | SweepstakeEntrySet): Promise<void> {
        const result = await this.validateSweepstakeAndEntry(entryIdOrRef);
        if (!result)
            return;

        const { entry, sweepstake } = result;
        const userId = entry.user.id;

        await this.buyInBalanceCache.lock(sweepstake.id, userId, async () => {
            if (entry.state === SweepstakeEntryState.Void) {
                Logger.warn(`Entry set ${entry.id} is already void. Skipping balance update.`);
                return;
            }

            const entryCount = entry.count;
            const { validatedBalance, create } = await this.reduceManualEntryCount(sweepstake, userId, entryCount);

            await this.saveBalanceAndNotify(validatedBalance, sweepstake, create);

            Logger.info(`Updated balance for user ${userId} in sweepstake ${sweepstake.id} after removing ${entryCount} manual entries.`);
        });
    }

    public async voidEntry(entryIdOrRef: number | SweepstakeEntrySet): Promise<void> {
        const result = await this.validateSweepstakeAndEntry(entryIdOrRef);
        if (!result)
            return;

        const { entry, sweepstake } = result;
        const userId = entry.user.id;

        await this.buyInBalanceCache.lock(sweepstake.id, userId, async () => {
            const entryCount = entry.count;
            const { validatedBalance, create } = await this.reduceManualEntryCount(sweepstake, userId, entryCount);

            await this.saveBalanceAndNotify(validatedBalance, sweepstake, create);

            Logger.info(`Updated balance for user ${userId} in sweepstake ${sweepstake.id} after voiding ${entryCount} manual entries.`);
        });
    }

    public async addAmount(idOrRef: number | Sweepstake, userId: number, amount: BigNumber, addEntrySet: boolean = true): Promise<SweepstakeGameBuyInBalance> {
        if (amount.isNegative() || amount.isZero())
            throw new BadRequestError('Amount cannot be negative or zero.');

        const sweepstake = await this.getValidSweepstake(idOrRef);

        if (!sweepstake)
            throw new BadRequestError(`Sweepstake ${idOrRef} is not a valid game buy-in entry sweepstake.`);

        return this.buyInBalanceCache.lock(sweepstake.id, userId, async () => {
            const { balance, create } = await this.getBalance(sweepstake, userId);
            const {
                manualAmountBase: currManualAmountBase,
                amountBase: currAmountBase,
                entryCount: currEntryCount
            } = balance;

            const adjustedEntryCount = this.getEntryAdjustment(sweepstake, currEntryCount, currAmountBase, amount, AmountOperation.Add);

            balance.entryCount = currEntryCount + adjustedEntryCount;
            balance.amountBase = balance.amountBase.plus(amount);
            balance.manualAmountBase = currManualAmountBase.plus(amount);

            const validatedBalance = this.getValidatedBalance(balance, sweepstake, userId);

            if (addEntrySet && adjustedEntryCount > 0)
                await this.addEntrySet(sweepstake, userId, adjustedEntryCount);

            await this.saveBalanceAndNotify(validatedBalance, sweepstake, create);

            Logger.info(`Added amount ${amount} for user ${userId} in sweepstake ${sweepstake.id}.`, validatedBalance);

            return validatedBalance;
        });
    }

    public async isGameBuyInEntrySweepstake(idOrRef: number | Sweepstake): Promise<boolean> {
        const sweepstake = await this.getValidSweepstake(idOrRef);
        return !!sweepstake;
    }

    public async reset(idOrRef: number | Sweepstake, userId: number, mode: SweepstakeGameBuyInBalanceResetMode): Promise<void> {
        const sweepstake = await this.getValidSweepstake(idOrRef);

        if (!sweepstake)
            throw new BadRequestError(`Sweepstake ${idOrRef} is not a valid game buy-in entry sweepstake.`);

        await this.buyInBalanceCache.lock(sweepstake.id, userId, async () => {
            await this.buyInBalanceCache.reset(sweepstake.id, userId, mode);

            const balance = await this.buyInBalanceCache.get(sweepstake.id, userId);

            if (balance) {
                await this.repository.update(balance);
                await this.notifyBalanceChange(balance, sweepstake);
            }

            Logger.info(`Reset ${mode} data for user ${userId} in sweepstake ${sweepstake.id}.`);
        });
    }

    public async remove(idOrRef: number | Sweepstake, userId: number): Promise<void> {
        const sweepstake = await this.getValidSweepstake(idOrRef);

        if (!sweepstake)
            throw new BadRequestError(`Sweepstake ${idOrRef} is not a valid game buy-in entry sweepstake.`);

        await this.buyInBalanceCache.lock(sweepstake.id, userId, async () => {
            await this.buyInBalanceCache.remove(sweepstake.id, userId);
            await this.repository.remove(sweepstake.id, userId);
            Logger.info(`Removed balance for user ${userId} in sweepstake ${sweepstake.id}.`);
        });
    }

    private async saveBalanceAndNotify(
        validatedBalance: SweepstakeGameBuyInBalance,
        sweepstake: PlatformSweepstake,
        create: boolean
    ): Promise<void> {

        if (create) {
            await this.buyInBalanceCache.store(sweepstake.closeTime, validatedBalance);
            await this.repository.add(validatedBalance);
        } else {
            await this.buyInBalanceCache.update(validatedBalance);
            await this.repository.update(validatedBalance);
        }

        await this.notifyBalanceChange(validatedBalance, sweepstake);
    }

    private async addEntrySet(sweepstake: Sweepstake, userId: number, entryCount: number): Promise<SweepstakeEntrySet> {
        const added = await this.entrySetManager.add({
            sweepstakeRef: sweepstake,
            userId,
            entryCount,
            skip: {
                payment: true
            }
        });

        Logger.info(`Added ${entryCount} entries for user ${userId} in sweepstake ${sweepstake.id}.`);

        return added;
    }

    private getSweepstakeTargetMultiplier(sweepstake: PlatformSweepstake): SweepstakeTargetMultiplier {
        const multiplier = Number(sweepstake.metadata?.gameBuyInEntryMultiplier || 1);
        const target = new BigNumber(sweepstake.metadata?.gameBuyInEntryTarget || 0);

        if (target.isZero())
            throw new ConflictError('Invalid sweepstake configuration: target cannot be zero.');

        return { multiplier, target };
    }

    private async getBalance(sweepstake: PlatformSweepstake, userId: number): Promise<CurrentBalance> {
        const balance = await this.buyInBalanceCache.get(sweepstake.id, userId);
        if (balance) {
            // for backward compatibility
            balance.manualAmountBase = balance.manualAmountBase || new BigNumber(0);
            balance.manualEntryAdditions = balance.manualEntryAdditions || 0;
            balance.manualEntryDeductions = balance.manualEntryDeductions || 0;

            return { balance, create: false };
        }

        return {
            balance: {
                sweepstakeId: sweepstake.id,
                userId,
                amountBase: new BigNumber(0),
                entryCount: 0,
                manualEntryAdditions: 0,
                manualEntryDeductions: 0,
                manualAmountBase: new BigNumber(0)
            },
            create: true
        };
    }

    private getValidatedBalance(balance: SweepstakeGameBuyInBalance, sweepstake: PlatformSweepstake, userId: number): SweepstakeGameBuyInBalance {
        if (balance.amountBase.isNegative()) {
            Logger.warn(`Amount base is negative. Adjusting for user ${userId} in sweepstake ${sweepstake.id}.`, balance);
            balance.amountBase = new BigNumber(0);
        }

        if (balance.manualAmountBase.isNegative()) {
            Logger.warn(`Manual amount base is negative. Adjusting for user ${userId} in sweepstake ${sweepstake.id}.`, balance);
            balance.manualAmountBase = new BigNumber(0);
        }

        return balance;
    }

    private async reduceManualEntryCount(
        sweepstake: PlatformSweepstake,
        userId: number,
        entryCount: number
    ): Promise<AfterDeductionResult> {
        const { balance, create } = await this.getBalance(sweepstake, userId);
        balance.manualEntryDeductions += entryCount;

        const validatedBalance = this.getValidatedBalance(balance, sweepstake, userId);

        return { validatedBalance, create };
    }

    private async validateSweepstakeAndEntry(entryIdOrRef: number | SweepstakeEntrySet): Promise<ValidSweepstakeEntry | undefined> {
        const entry = typeof entryIdOrRef === 'number' ? await this.entrySetManager.getById(entryIdOrRef) : entryIdOrRef;
        if (!entry)
            return undefined;

        const sweepstake = await this.getValidSweepstake(entry.sweepstake.id);
        if (!sweepstake)
            return undefined;

        return { entry, sweepstake };
    }

    private async getValidSweepstake(idOrRef: number | Sweepstake): Promise<PlatformSweepstake | undefined> {
        const sweepstake = typeof idOrRef === 'number'
            ? await this.sweepstakeManager.getActive<ActivePlatformSweepstake>(idOrRef)
            : idOrRef;

        if (!sweepstake || sweepstake.type !== SweepstakeType.Platform || !sweepstake.metadata?.gameBuyInEntryTarget) {
            Logger.warn(`Sweepstake ${typeof idOrRef === 'number' ? idOrRef : sweepstake.id} is not a valid game buy-in entry sweepstake.`);
            return undefined;
        }

        return sweepstake;
    }

    private getEntryAdjustment(
        sweepstake: PlatformSweepstake,
        currEntryCount: number,
        currAmount: BigNumber,
        amount: BigNumber,
        operation: AmountOperation
    ): number {
        const newEntryCount = this.getNewEntryCount(sweepstake, currAmount, amount, operation);

        return Math.max(newEntryCount - currEntryCount, 0);
    }

    private getNewEntryCount(
        sweepstake: PlatformSweepstake,
        currAmount: BigNumber,
        amount: BigNumber,
        operation: AmountOperation
    ): number {
        const { target, multiplier } = this.getSweepstakeTargetMultiplier(sweepstake);
        const newAmount = operation === AmountOperation.Add ? currAmount.plus(amount) : currAmount.minus(amount);

        if (newAmount.isNegative())
            return 0;

        return newAmount
            .dividedBy(target)
            .multipliedBy(multiplier)
            .decimalPlaces(0, BigNumber.ROUND_DOWN)
            .toNumber();
    }

    private async notifyBalanceChange(balance: SweepstakeGameBuyInBalance, sweepstake: PlatformSweepstake): Promise<void> {
        const model = await this.buyInBalanceMapper.fromBalance(balance, sweepstake);
        if (!model) {
            Logger.warn(`Failed to map balance change for user ${balance.userId} in sweepstake ${sweepstake.id}.`, balance);
            return;
        }

        await this.websocket
            .user(model.userId)
            .type('Sweepstake:GameBuyIn:Balance:Change')
            .payload(model)
            .send();
    }
}
