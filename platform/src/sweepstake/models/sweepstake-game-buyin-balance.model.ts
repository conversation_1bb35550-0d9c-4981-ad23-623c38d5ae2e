import { BigNumber } from '../../core';

export interface SweepstakeGameBuyInEntry {
    id: number;
    count: number;
    code: string;
}

export interface SweepstakeGameBuyInBalanceModel {
    sweepstake: {
        id: number;
        name: string;
        closeTime?: Date;
        drawTime?: Date;
    };
    userId: number;
    currencyCode: string;
    amount: BigNumber; // The total effectiveAmountBase recorded for the player
    entryCount: number;
    entries?: SweepstakeGameBuyInEntry[];
    target: {
        value: BigNumber;
        multiplier: number;
        remaining: BigNumber;
        progress: number;
    };
}