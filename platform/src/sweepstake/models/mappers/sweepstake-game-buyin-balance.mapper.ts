import { Inject, Singleton } from '../../../core/ioc';
import { BigNumber } from '../../../core/big-number';
import { Config, NotFoundError } from '../../../core';
import { Sweepstake } from '../../sweepstake';
import { SweepstakeManager } from '../../sweepstake-manager';
import { SweepstakeGameBuyInBalanceCache } from '../../cache';
import { SweepstakeGameBuyInBalance } from '../../sweepstake-game-buyin';
import { SweepstakeGameBuyInBalanceModel, SweepstakeGameBuyInEntry } from '../sweepstake-game-buyin-balance.model';
import { getEffectiveTotalEntryCount } from '../../../sweepstake/utilities/sweepstake-game-buyin-utility';
import { SweepstakeEntrySetManager } from '../../sweepstake-entry-set-manager';
import { SweepstakeEntrySetCodeGenerator } from '../../utilities/sweepstake-entryset-code.generator';
import { SweepstakeGameBuyInBalanceRepository } from '../../repositories';
import { SweepstakeState } from '../../sweepstake-state';

@Singleton
export class SweepstakeGameBuyInBalanceModelMapper {
    constructor(
        @Inject private readonly sweepstakeManager: SweepstakeManager,
        @Inject private readonly entrySetManager: SweepstakeEntrySetManager,
        @Inject private readonly codeGenerator: SweepstakeEntrySetCodeGenerator,
        @Inject private readonly cache: SweepstakeGameBuyInBalanceCache,
        @Inject private readonly repository: SweepstakeGameBuyInBalanceRepository) {
    }

    public async from(sweepstakeId: number, userId: number): Promise<SweepstakeGameBuyInBalanceModel>;
    public async from(sweepstake: Sweepstake, userId: number): Promise<SweepstakeGameBuyInBalanceModel>;
    public async from(sweepstakeIdOrRef: number | Sweepstake, userId: number): Promise<SweepstakeGameBuyInBalanceModel> {
        const sweepstake = typeof sweepstakeIdOrRef === 'number'
            ? await this.sweepstakeManager.get(sweepstakeIdOrRef)
            : sweepstakeIdOrRef;

        if (!sweepstake)
            throw new NotFoundError('Sweepstake not found');

        const buyInEntryTarget = new BigNumber(sweepstake.metadata?.gameBuyInEntryTarget || 0);
        const buyInEntryMultiplier = Number(sweepstake.metadata?.gameBuyInEntryMultiplier || 1);
        const DEFAULT_MODEL: SweepstakeGameBuyInBalanceModel = {
            sweepstake: {
                id: sweepstake.id,
                name: sweepstake.name,
                closeTime: sweepstake.closeTime,
                drawTime: sweepstake.drawTime
            },
            userId,
            currencyCode: Config.baseCurrency,
            amount: new BigNumber(0),
            entryCount: 0,
            target: {
                value: buyInEntryTarget,
                remaining: buyInEntryTarget,
                multiplier: buyInEntryMultiplier,
                progress: 0
            }
        };

        let balance = await this.cache.get(sweepstake.id, userId);

        if (!balance && sweepstake.state >= SweepstakeState.Complete)
            balance = await this.repository.get(sweepstake.id, userId);

        if (!balance)
            return DEFAULT_MODEL;

        const model = await this.fromBalance(balance, sweepstake, sweepstake.metadata?.gameBuyInMapEntries || false);
        return model ?? DEFAULT_MODEL;
    }

    public async fromBalance(source: SweepstakeGameBuyInBalance, sweepstakeRef?: Sweepstake, includeEntries: boolean = false): Promise<SweepstakeGameBuyInBalanceModel | undefined> {
        const sweepstake = sweepstakeRef ?? await this.sweepstakeManager.get(source.sweepstakeId);

        if (!sweepstake || sweepstake.id !== source.sweepstakeId)
            return;

        const buyInEntryTarget = new BigNumber(sweepstake.metadata?.gameBuyInEntryTarget || 0);

        if (buyInEntryTarget.isZero())
            return;

        const buyInEntryMultiplier = Number(sweepstake.metadata?.gameBuyInEntryMultiplier || 1);

        let remaining = new BigNumber(0);
        let progress = 0;

        // Calculate the remainder (progress towards the next threshold).
        const usedAmount = buyInEntryTarget.times(source.entryCount).dividedBy(buyInEntryMultiplier);
        const availableAmount = source.amountBase.minus(usedAmount);

        if (availableAmount.isGreaterThan(buyInEntryTarget))
            progress = 100;
        else {
            const remainder = availableAmount.mod(buyInEntryTarget);
            const remainingAmount = buyInEntryTarget.minus(remainder);

            remaining = remainingAmount.isGreaterThan(buyInEntryTarget)
                ? buyInEntryTarget
                : remainingAmount;

            progress = remainder
                .dividedBy(buyInEntryTarget)
                .multipliedBy(100)
                .decimalPlaces(0, BigNumber.ROUND_DOWN)
                .toNumber();
        }

        const entries = includeEntries
            ? await this.getEntries(source.sweepstakeId, source.userId)
            : undefined;

        return {
            sweepstake: {
                id: sweepstake.id,
                name: sweepstake.name,
                closeTime: sweepstake.closeTime,
                drawTime: sweepstake.drawTime
            },
            userId: source.userId,
            currencyCode: Config.baseCurrency,
            amount: source.amountBase,
            entryCount: getEffectiveTotalEntryCount(source),
            entries,
            target: {
                value: buyInEntryTarget,
                multiplier: buyInEntryMultiplier,
                progress: Math.max(progress, 0),
                remaining
            }
        };
    }

    private async getEntries(sweepstakeId: number, userId: number): Promise<SweepstakeGameBuyInEntry[]> {
        const entrySets = await this.entrySetManager.get(sweepstakeId, userId);

        if (entrySets.length === 0)
            return [];

        return entrySets.map(entrySet => ({
            id: entrySet.id,
            count: entrySet.count,
            code: this.codeGenerator.generate(entrySet.id, entrySet.count)
        }));
    }
}