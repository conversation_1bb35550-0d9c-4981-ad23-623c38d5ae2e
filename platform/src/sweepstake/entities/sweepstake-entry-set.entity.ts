import {
    ManyTo<PERSON>ne,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Column,
    Entity,
    Index,
    OneToOne,
    OneToMany,
    BigNumberTransformer,
    NumericTransformer,
    BigIntPrimaryGeneratedColumn,
    BigIntColumn
} from '../../core/db/orm';
import { BigNumber } from '../../core';
import { UserEntity } from '../../user/entities/user.entity';
import { FundsContext } from '../../banking/funds-context';
import { WalletEntryEntity } from '../../banking/entities';
import { SweepstakeEntryState } from '../sweepstake-entry-state';
import { SweepstakeEntity } from './sweepstake.entity';
import { SweepstakePrizeAwardEntity } from './sweepstake-prize-award.entity';

@Entity()
@Index(['userId', 'sweepstakeId'], { unique: false })
export class SweepstakeEntrySetEntity {
    @BigIntPrimaryGeneratedColumn()
    public id!: number;

    @Column({ type: 'enum', enum: SweepstakeEntryState, default: SweepstakeEntryState.Pending })
    public state!: SweepstakeEntryState;

    @Index()
    @BigIntColumn()
    public userId!: number;

    @ManyToOne(() => UserEntity)
    public user!: UserEntity;

    @BigIntColumn()
    public sweepstakeId!: number;

    @ManyToOne(() => SweepstakeEntity)
    public sweepstake!: SweepstakeEntity;

    @BigIntColumn({ nullable: true })
    public packageId?: number;

    @BigIntColumn()
    public count!: number;

    @Column({ type: 'decimal', precision: 33, scale: 18, transformer: new BigNumberTransformer(), default: 0 })
    public cost!: BigNumber;

    @Column({ type: 'decimal', precision: 33, scale: 18, transformer: new BigNumberTransformer(), default: 0 })
    public commission!: BigNumber;

    @Column({ type: 'decimal', precision: 16, scale: 4, transformer: new NumericTransformer(), default: 0 })
    public commissionRate!: number;

    @Column({ nullable: true })
    public externalId?: string;

    @Column({ nullable: true })
    public externalRefundId?: string;

    @BigIntColumn({ nullable: true })
    public walletEntryId?: number;

    @OneToOne(() => WalletEntryEntity, { nullable: true })
    @JoinColumn()
    public walletEntry?: WalletEntryEntity;

    @OneToMany(() => SweepstakePrizeAwardEntity, e => e.entrySet)
    public awards!: SweepstakePrizeAwardEntity[];

    @Column({ type: 'enum', enum: FundsContext, nullable: true })
    public fundsContext?: FundsContext;

    @Column({ nullable: true })
    public awardTime?: Date;

    @Column({ nullable: true })
    public refundTime?: Date;

    @Column({ nullable: true })
    public voidTime?: Date;

    @Column({ nullable: true })
    public payoutTime?: Date;

    @Column({ type: 'datetime', precision: 6, default: () => 'CURRENT_TIMESTAMP(6)' })
    public createTime!: Date;

    @Column({ type: 'datetime', precision: 6, default: () => 'CURRENT_TIMESTAMP(6)' })
    public updateTime!: Date;

    @Column({ type: 'decimal', precision: 33, scale: 18, transformer: new BigNumberTransformer(), nullable: true })
    public sourceCost?: BigNumber;

    @Column({ type: 'decimal', precision: 33, scale: 18, transformer: new BigNumberTransformer(), nullable: true })
    public sourceRate?: BigNumber;

    @Column({ nullable: true })
    public sourceCurrency?: string;

    @Column({ nullable: true, length: 50 })
    public sourceBrand?: string;

    @Column({ nullable: true, length: 100 })
    public source?: string;
}