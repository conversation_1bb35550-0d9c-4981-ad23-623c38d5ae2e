import { Inject, Singleton } from '../../../core/ioc';
import { BigNumber } from '../../../core/big-number';
import { Config } from '../../../core';
import { FundsContext } from '../../../banking/funds-context';
import { SweepstakeEntrySetEntity } from '../sweepstake-entry-set.entity';
import { NewSweepstakeEntrySet, SweepstakeEntrySet } from '../../sweepstake-entry-set';
import { SweepstakePrizeAwardEntityMapper } from './sweepstake-prize-award.mapper';
import { SweepstakeType } from '../../../sweepstake/sweepstake-type';
import { BlockchainSweepstakeEntity } from '../sweepstake.entity';

@Singleton
export class SweepstakeEntrySetEntityMapper {
    constructor(
        @Inject private readonly awardMapper: SweepstakePrizeAwardEntityMapper) {
    }

    public newToEntity(source: NewSweepstakeEntrySet): SweepstakeEntrySetEntity {
        const entity = new SweepstakeEntrySetEntity();
        entity.state = source.state;
        entity.userId = source.userId;
        entity.sweepstakeId = source.sweepstakeId;
        entity.count = source.count;
        entity.cost = source.cost || new BigNumber(0);
        entity.commissionRate = source.commissionRate || 0;
        entity.commission = source.commission || new BigNumber(0);

        if (source.externalId)
            entity.externalId = source.externalId;

        if (source.packageId)
            entity.packageId = source.packageId;

        if (source.walletEntryId)
            entity.walletEntryId = source.walletEntryId;

        if (source.fundsContext)
            entity.fundsContext = source.fundsContext;

        if (source.createTime)
            entity.createTime = entity.updateTime = source.createTime;

        if (source.sourceCost)
            entity.sourceCost = source.sourceCost;

        if (source.sourceRate)
            entity.sourceRate = source.sourceRate;

        if (source.sourceCurrency)
            entity.sourceCurrency = source.sourceCurrency;

        if (source.sourceBrand)
            entity.sourceBrand = source.sourceBrand;

        if (source.source)
            entity.source = source.source;

        return entity;
    }

    public fromEntities(source: SweepstakeEntrySetEntity[]): SweepstakeEntrySet[] {
        if (!source || source.length === 0)
            return [];

        return source.map(s => this.fromEntity(s));
    }

    public fromEntity(source: SweepstakeEntrySetEntity): SweepstakeEntrySet {
        const set: SweepstakeEntrySet = {
            id: source.id,
            state: source.state,
            sweepstake: {
                id: source.sweepstakeId,
                type: source.sweepstake.type,
                name: source.sweepstake.name,
                network: source.sweepstake.type === SweepstakeType.Blockchain ? (source.sweepstake as BlockchainSweepstakeEntity).network : undefined,
                metadata: source.sweepstake.metadata
            },
            user: {
                id: source.userId,
                displayName: source.user.displayName || 'Anonymous'
            },
            count: source.count,
            currencyCode: source.sweepstake.currencyCode,
            cost: source.cost,
            commission: source.commission,
            commissionRate: source.commissionRate,
            totalCost: source.cost.plus(source.commission),
            awards: this.awardMapper.fromEntities(source.awards),
            fundsContext: source.fundsContext || FundsContext.Default,
            sourceBrand: source.sourceBrand || Config.brand,
            createTime: source.createTime,
            updateTime: source.updateTime
        };

        if (source.externalId)
            set.externalId = source.externalId;

        if (source.externalRefundId)
            set.externalRefundId = source.externalRefundId;

        if (source.packageId)
            set.packageId = source.packageId;

        if (source.walletEntryId)
            set.walletEntryId = source.walletEntryId;

        if (source.refundTime)
            set.refundTime = source.refundTime;

        if (source.voidTime)
            set.voidTime = source.voidTime;

        if (source.awardTime)
            set.awardTime = source.awardTime;

        if (source.payoutTime)
            set.payoutTime = source.payoutTime;

        if (source.sourceCost)
            set.sourceCost = source.sourceCost;

        if (source.sourceRate)
            set.sourceRate = source.sourceRate;

        if (source.sourceCurrency)
            set.sourceCurrency = source.sourceCurrency;

        if (source.source)
            set.source = source.source;

        return set;
    }
}