import { Inject, Singleton } from '../core/ioc';
import Logger, { LogClass, LogLevel } from '../core/logging';
import { Config } from '../core';
import { CriticalError, ForbiddenError, NotFoundError } from '../core/errors';
import { UserAliasType, UserManager } from '../user';
import { SweepstakeManager } from './sweepstake-manager';
import { BlockchainSweepstake } from './sweepstake';
import { SweepstakeEntryState } from './sweepstake-entry-state';
import { SweepstakeFreeEntryCache } from './cache';
import { SweepstakeType } from './sweepstake-type';
import { SweepstakeEntrySetManager } from './sweepstake-entry-set-manager';

export interface SweepstakeFreeEntryBulkResult {
    pending: number;
    ignored: string[];
}

@Singleton
@LogClass({ level: LogLevel.Info })
export class SweepstakeFreeEntryBulkProcessor {
    constructor(
        @Inject private readonly userManager: UserManager,
        @Inject private readonly sweepstakeManager: SweepstakeManager,
        @Inject private readonly sweepstakeEntrySetManager: SweepstakeEntrySetManager,
        @Inject private readonly sweepstakeFreeEntryCache: SweepstakeFreeEntryCache) {
    }

    public async process(idOrRef: number | BlockchainSweepstake, userAliases: string[], packageId?: number): Promise<SweepstakeFreeEntryBulkResult> {
        const sweepstake = typeof idOrRef === 'number'
            ? await this.sweepstakeManager.get<BlockchainSweepstake>(idOrRef)
            : idOrRef;

        if (!sweepstake)
            throw new NotFoundError(`Sweepstake ${idOrRef} not found.`);

        if (sweepstake.type !== SweepstakeType.Blockchain)
            throw new ForbiddenError(`Sweepstake ${sweepstake.id} is incorrect type.`);

        if (!sweepstake.externalId)
            throw new ForbiddenError(`Sweepstake ${sweepstake.id} does not have an external ID.`);

        const entryPackage = packageId
            ? sweepstake.entryPackages.find(e => e.id === packageId)
            : undefined;

        if (packageId && !entryPackage)
            throw new CriticalError(`Sweepstake entry package ${packageId} not found.`);

        if (entryPackage?.price.isGreaterThan(0))
            throw new ForbiddenError(`Sweepstake entry package ${packageId} price is greater than 0.`);

        const pending: number[] = [];
        const ignored: string[] = [];

        for (const userAlias of userAliases) {
            const user = await this.userManager.getByAlias(UserAliasType.EthereumAddress, userAlias);

            if (!user) {
                Logger.warn(`User with alias ${userAlias} not found.`);
                ignored.push(userAlias);
                continue;
            }

            const added = await this.sweepstakeEntrySetManager.add({
                sweepstakeRef: sweepstake,
                userId: user.id,
                packageId,
                state: SweepstakeEntryState.Pending,
                brand: Config.brand,
                source: !packageId ? 'AMOE' : undefined,
                skip: {
                    event: true,
                    websocket: true
                }
            });

            await this.sweepstakeFreeEntryCache.store(sweepstake.id, {
                userId: user.id,
                userAlias,
                entrySetId: added.id,
                count: added.count
            });

            pending.push(added.id);
        }

        return {
            pending: pending.length,
            ignored
        };
    }
}