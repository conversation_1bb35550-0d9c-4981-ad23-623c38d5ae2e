import { Inject, Singleton } from '../../../core/ioc';
import { CriticalError, ForbiddenError, NotFoundError } from '../../../core/errors';
import Logger, { LogClass, LogLevel } from '../../../core/logging';
import { Config } from '../../../core';
import { UserManager, User, UserAliasType } from '../../../user';
import { isGreaterOrEqualToMaxPackagePerUser } from '../../utilities';
import { SweepstakeFreeEntryCache } from '../../cache';
import { SweepstakeManager } from '../../sweepstake-manager';
import { SweepstakeEntrySet } from '../../sweepstake-entry-set';
import { SweepstakeEntrySetManager } from '../../sweepstake-entry-set-manager';
import { SweepstakeEntryState } from '../../sweepstake-entry-state';
import { BlockchainSweepstake } from '../../sweepstake';
import { ActiveBlockchainSweepstake } from '../../sweepstake-active';
import { SweepstakeAddEntryOptions, SweepstakeEntryMediator } from '../sweepstake-entry-mediator';
import { getPrimaryAlias } from '../../../user/utilities';

@Singleton
@LogClass({ level: LogLevel.Info })
export class BlockchainSweepstakeEntryMediator implements SweepstakeEntryMediator {
    constructor(
        @Inject private readonly sweepstakeManager: SweepstakeManager,
        @Inject private readonly sweepstakeEntrySetManager: SweepstakeEntrySetManager,
        @Inject private readonly sweepstakeFreeEntryCache: SweepstakeFreeEntryCache,
        @Inject private readonly userManager: UserManager) {
    }

    public async add(idOrRef: number | BlockchainSweepstake, userIdOrRef: number | User, options?: SweepstakeAddEntryOptions): Promise<SweepstakeEntrySet> {
        const { packageId, source } = options || {};
        Logger.info('Creating blockchain sweepstake entry set...', { idOrRef, userIdOrRef, packageId });

        const sweepstake = typeof idOrRef === 'number'
            ? await this.sweepstakeManager.getActive<ActiveBlockchainSweepstake>(idOrRef)
            : idOrRef;

        if (!sweepstake.externalId)
            throw new NotFoundError(`Sweepstake ${sweepstake.id} external ID not found.`);

        const user = typeof userIdOrRef === 'number'
            ? await this.userManager.get(userIdOrRef)
            : userIdOrRef;

        if (!user)
            throw new NotFoundError(`User ${userIdOrRef} not found.`);

        const userAlias = getPrimaryAlias(user, UserAliasType.EthereumAddress);

        if (!userAlias)
            throw new NotFoundError(`User ${userIdOrRef} Ethereum address not found`);

        const entryPackage = packageId
            ? sweepstake.entryPackages.find(e => e.id === packageId)
            : undefined;

        if (packageId && !entryPackage)
            throw new CriticalError(`Sweepstake entry package ${packageId} not found.`);

        if (entryPackage?.price.isGreaterThan(0))
            throw new ForbiddenError(`Sweepstake entry package ${packageId} price is greater than 0.`);

        if (entryPackage) {
            const entrySets = await this.sweepstakeEntrySetManager.getActive(sweepstake.id, user.id);

            if (isGreaterOrEqualToMaxPackagePerUser(entryPackage, entrySets))
                throw new ForbiddenError(`User ${user.id} has purchased maximum allowance of sweepstake entry package ${packageId}.`);
        }

        const added = await this.sweepstakeEntrySetManager.add({
            sweepstakeRef: sweepstake,
            userId: user.id,
            packageId,
            state: SweepstakeEntryState.Pending,
            brand: Config.brand,
            source,
            skip: {
                event: true,
                websocket: true
            }
        });

        await this.sweepstakeFreeEntryCache.store(sweepstake.id, {
            userId: user.id,
            userAlias,
            entrySetId: added.id,
            count: added.count
        });

        return added;
    }
}