import { Inject, Singleton } from '../../../core/ioc';
import { LogClass } from '../../../core/logging';
import { Config } from '../../../core/config';
import { User } from '../../../user';
import { SweepstakeEntrySet } from '../../sweepstake-entry-set';
import { PlatformSweepstake } from '../../sweepstake';
import { SweepstakeEntrySetManager } from '../../sweepstake-entry-set-manager';
import { SweepstakeAddEntryOptions, SweepstakeEntryMediator } from '../sweepstake-entry-mediator';

@Singleton
@LogClass()
export class PlatformSweepstakeEntryMediator implements SweepstakeEntryMediator {
    constructor(
        @Inject private readonly sweepstakeEntrySetManager: SweepstakeEntrySetManager) {
    }

    public async add(idOrRef: number | PlatformSweepstake, userIdOrRef: number | User, options?: SweepstakeAddEntryOptions): Promise<SweepstakeEntrySet> {
        const userId = typeof userIdOrRef === 'number'
            ? userIdOrRef
            : userIdOrRef.id;

        const { packageId, fundsContext, currency, skipPayment, entryCount, source } = options || {};

        return this.sweepstakeEntrySetManager.add({
            sweepstakeRef: idOrRef,
            userId,
            packageId,
            skip: {
                payment: skipPayment
            },
            brand: Config.brand,
            fundsContext,
            currencyCode: currency,
            entryCount,
            source
        });
    }
}