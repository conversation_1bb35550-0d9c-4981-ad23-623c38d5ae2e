import { User } from '../../user';
import { SweepstakeEntrySet } from '../sweepstake-entry-set';
import { Sweepstake } from '../sweepstake';
import { FundsContext } from '../../banking';

export interface SweepstakeAddEntryOptions {
    packageId?: number;
    skipPayment?: boolean;
    fundsContext?: FundsContext;
    currency?: string;
    entryCount?: number;
    source?: string;
}

export interface SweepstakeEntryMediator {
    add(idOrRef: number | Sweepstake, userIdOrRef: number | User, options?: SweepstakeAddEntryOptions): Promise<SweepstakeEntrySet>;
}