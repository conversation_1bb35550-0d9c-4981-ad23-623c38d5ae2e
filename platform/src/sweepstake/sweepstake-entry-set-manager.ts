import { <PERSON><PERSON><PERSON><PERSON>, CriticalError, ForbiddenError, NotFoundError, PagedResult, Config, RequesterType } from '../core';
import { Singleton, Inject } from '../core/ioc';
import Logger, { LogClass, UserLog } from '../core/logging';
import { PlatformEventDispatcher } from '../core/events';
import { Websocket } from '../websocket';
import { CurrencyManager, FundsContext, Ledger, PlatformWallets, TransactionPurpose, UserWalletAccountResolver, UserWalletAccountResolverResult, UserWalletAccounts } from '../banking';
import { TransferResult } from '../banking/transfer';
import { SweepstakeEntrySetAddedEvent } from './events';
import { SweepstakeEntrySetRepository } from './repositories';
import { SweepstakeActivityCache, SweepstakeEntrySetCache, SweepstakeCurrencyRateCache } from './cache';
import { SweepstakeCacheExpiryMapper } from './cache/mappers';
import { isGreaterOrEqualToMaxPackagePerUser, isGreaterThanMaxEntry, isGreaterThanMaxEntryPerUser, isBaseCurrency, currencyMatch } from './utilities';
import { SweepstakePrize } from './prizes/sweepstake-prize';
import { SweepstakeUserActivityFilter, SweepstakeEntrySetFilter, SweepstakeEntrySetQuantityFilter } from './sweepstake-filter';
import { SweepstakeManager } from './sweepstake-manager';
import { SweepstakeParticipantManager } from './sweepstake-participant-manager';
import { SweepstakePrizeAwardManager } from './sweepstake-prize-award-manager';
import { NewSweepstakeEntrySet, SweepstakeEntrySet, SweepstakeEntrySetAwarded, SweepstakeEntrySetWinner } from './sweepstake-entry-set';
import { SweepstakeEntrySetQuantityResult } from './sweepstake-entry-set-quantity-result';
import { SweepstakeEntryConfirmSkipOptions } from './sweepstake-entry-skip-options';
import { SweepstakeEntryPriceType } from './sweepstake-entry-price-type';
import { SweepstakeEntryState } from './sweepstake-entry-state';
import { SweepstakeEntryParams } from './sweepstake-entry-params';
import { SweepstakeUserEntry } from './sweepstake-user-entry';
import { SweepstakeUserActivity } from './sweepstake-user-activity';
import { SweepstakeState } from './sweepstake-state';
import { SweepstakeType } from './sweepstake-type';
import { Sweepstake } from './sweepstake';
import _ from 'lodash';
import { getEffectiveAmount } from '../banking/utilities';

@Singleton
@LogClass()
export class SweepstakeEntrySetManager {
    constructor(
        @Inject private readonly sweepstakeManager: SweepstakeManager,
        @Inject private readonly entrySetRepository: SweepstakeEntrySetRepository,
        @Inject private readonly entrySetCache: SweepstakeEntrySetCache,
        @Inject private readonly participantManager: SweepstakeParticipantManager,
        @Inject private readonly prizeAwardManager: SweepstakePrizeAwardManager,
        @Inject private readonly cacheExpiryMapper: SweepstakeCacheExpiryMapper,
        @Inject private readonly activityCache: SweepstakeActivityCache,
        @Inject private readonly eventDispatcher: PlatformEventDispatcher,
        @Inject private readonly currencyManager: CurrencyManager,
        @Inject private readonly userWalletAccountResolver: UserWalletAccountResolver,
        @Inject private readonly userLog: UserLog,
        @Inject private readonly ledger: Ledger,
        @Inject private readonly websocket: Websocket,
        @Inject private readonly currencyRateCache: SweepstakeCurrencyRateCache) {
    }

    public async getAll(filter: SweepstakeEntrySetFilter): Promise<PagedResult<SweepstakeEntrySet>> {
        return this.entrySetRepository.getAll(filter);
    }

    public async getMany(sweepstakeId: number, ids: number[]): Promise<SweepstakeEntrySet[]> {
        return this.entrySetRepository.getMany(sweepstakeId, ids);
    }

    public async get(sweepstakeId: number, userId: number): Promise<SweepstakeEntrySet[]> {
        const cachedItems = await this.entrySetCache.get(sweepstakeId, userId);

        if (cachedItems.length > 0)
            return cachedItems;

        const entrySets = await this.entrySetRepository.get(sweepstakeId, userId);

        if (entrySets.length === 0)
            return [];

        const sweepstake = await this.sweepstakeManager.get(sweepstakeId);

        if (!sweepstake)
            return entrySets;

        const expireAt = await this.cacheExpiryMapper.from(sweepstake);
        await this.entrySetCache.store(sweepstakeId, userId, entrySets, expireAt);

        return entrySets;
    }

    public async getActive(sweepstakeId: number, userId: number, cacheOnly: boolean = false): Promise<SweepstakeEntrySet[]> {
        const cachedItems = await this.entrySetCache.getActive(sweepstakeId, userId);

        if (cachedItems.length > 0 || cacheOnly)
            return cachedItems;

        return this.entrySetRepository.getActive(sweepstakeId, userId);
    }

    public async getConfirmed(sweepstakeId: number, userId: number, cacheOnly: boolean = false): Promise<SweepstakeEntrySet[]> {
        const entrySets = await this.getActive(sweepstakeId, userId, cacheOnly);

        if (entrySets.length === 0)
            return [];

        return _.filter(entrySets, i => i.state === SweepstakeEntryState.Confirmed);
    }

    public async getForAwarding(sweepstakeId: number, userId: number, cacheOnly: boolean = false): Promise<SweepstakeEntrySet[]> {
        const entrySets = await this.getActive(sweepstakeId, userId, cacheOnly);

        if (entrySets.length === 0)
            return [];

        const states: SweepstakeEntryState[] = [
            SweepstakeEntryState.Confirmed,
            SweepstakeEntryState.Awarded,
            SweepstakeEntryState.Payout
        ];

        return _.filter(entrySets, e => states.includes(e.state));
    }

    public async getById(id: number): Promise<SweepstakeEntrySet | undefined> {
        const cachedItem = await this.entrySetCache.getById(id);

        if (cachedItem)
            return cachedItem;

        return this.entrySetRepository.getById(id);
    }

    public async getByExternalId(sweepstakeId: number, userId: number, externalId: string): Promise<SweepstakeEntrySet[]> {
        return this.entrySetRepository.getByExternalId(sweepstakeId, userId, externalId);
    }

    public async getBySweepstakeId(id: number, brand?: string): Promise<SweepstakeEntrySet[]> {
        let items = await this.entrySetCache.getAll(id);

        if (items.length === 0)
            items = await this.entrySetRepository.getBySweepstakeId(id);

        if (items.length === 0)
            return [];

        return brand ? _.filter(items, i => i.sourceBrand === brand) : items;
    }

    public async getAwarded(sweepstakeId: number, userId?: number): Promise<SweepstakeEntrySetAwarded[]> {
        const cachedItems = await this.entrySetCache.getAwarded(sweepstakeId, userId);

        if (cachedItems.length > 0)
            return cachedItems;

        return this.entrySetRepository.getAwarded(sweepstakeId, userId);
    }

    public async getWinners(sweepstakeId: number, userId?: number, brand?: string): Promise<SweepstakeEntrySetWinner[]> {
        let items = await this.entrySetCache.getWinners(sweepstakeId, userId);

        if (items.length === 0)
            items = await this.entrySetRepository.getWinners(sweepstakeId, userId);

        if (items.length === 0)
            return [];

        return brand ? _.filter(items, i => i.sourceBrand === brand) : items;
    }

    public async getRecentWinners(skip?: number, take?: number): Promise<SweepstakeEntrySetWinner[]> {
        return this.entrySetRepository.getRecentWinners(skip, take);
    }

    public async getRefundable(sweepstakeId: number, userId?: number): Promise<SweepstakeEntrySet[]> {
        return this.entrySetRepository.getRefundable(sweepstakeId, userId);
    }

    public async getVoidable(sweepstakeId: number): Promise<SweepstakeEntrySet[]> {
        return this.entrySetRepository.getVoidable(sweepstakeId);
    }

    public async getAllActivity(sweepstakeId: number): Promise<SweepstakeUserEntry[]> {
        return this.entrySetRepository.getActivity(sweepstakeId);
    }

    public async getActivity(sweepstakeId: number, skip: number = 0, take: number = 100): Promise<SweepstakeUserEntry[]> {
        const cachedItems = await this.activityCache.getAll(sweepstakeId, skip, take);

        if (cachedItems.length > 0)
            return cachedItems;

        return this.entrySetRepository.getActivity(sweepstakeId, skip, take);
    }

    public async getActivityByUser(filter: SweepstakeUserActivityFilter): Promise<PagedResult<SweepstakeUserActivity>> {
        return this.entrySetRepository.getActivityByUser(filter);
    }

    public async getEntryCount(sweepstakeId: number, userId: number, date?: Date, state?: SweepstakeEntryState): Promise<number> {
        const entrySets = await this.entrySetRepository.get(sweepstakeId, userId, date, state);
        return _.sumBy(entrySets, e => e.count);
    }

    public async getSetCount(sweepstakeId: number, userId: number): Promise<number> {
        return this.entrySetRepository.getSetCount(sweepstakeId, userId);
    }

    public async getCounts(sweepstakeId: number, userId: number): Promise<[number, number]> {
        return [
            await this.getSetCount(sweepstakeId, userId),
            await this.getEntryCount(sweepstakeId, userId)
        ];
    }

    public async getQuantity(filter: SweepstakeEntrySetQuantityFilter): Promise<SweepstakeEntrySetQuantityResult> {
        return this.entrySetRepository.getQuantity(filter);
    }

    public async exists(sweepstakeId: number, userId: number, externalId: string): Promise<boolean> {
        return this.entrySetRepository.exists(sweepstakeId, userId, externalId);
    }

    public async add(params: SweepstakeEntryParams): Promise<SweepstakeEntrySet> {
        const {
            sweepstakeRef,
            userId,
            packageId,
            skip,
            state,
            externalId,
            createTime,
            fundsContext,
            brand,
            entryCount: entryCountOverride,
            source
        } = params;

        const sweepstake = typeof sweepstakeRef === 'number'
            ? await this.sweepstakeManager.getActive(sweepstakeRef)
            : sweepstakeRef;

        const currencyCode = params.currencyCode || sweepstake.currencyCode;
        const currency = await this.currencyManager.get(currencyCode);

        if (!params.skip?.payment) {
            if (!currency || !currency.active)
                throw new ForbiddenError(`Unsupported currency ${params.currencyCode}.`);

            if (sweepstake.type === SweepstakeType.Platform && !currency.playable)
                throw new ForbiddenError(`Unsupported currency ${params.currencyCode}.`);
        }

        return this.entrySetCache.lock(sweepstake.id, userId, async () => {
            return this.userLog.handle(userId, 'Sweepstake:Entry:Set:Add', async (log) => {
                log.sweepstakeId = sweepstake.id;
                log.packageId = packageId ?? 0;
                log.skipPayment = skip?.payment ?? false;
                log.fundsContext = fundsContext || '';
                log.currencyCode = currencyCode;
                log.brand = brand || '';
                log.source = source || '';

                const entrySets = await this.entrySetCache.getActive(sweepstake.id, userId);

                const entryPackage = packageId
                    ? _.find(sweepstake.entryPackages, e => e.id === packageId)
                    : undefined;

                if (packageId && !entryPackage)
                    throw new CriticalError(`Sweepstake ${sweepstake.id} entry package ${packageId} not found.`);

                if (entryPackage && !entryPackage.enabled)
                    throw new ForbiddenError(`Sweepstake ${sweepstake.id} entry package ${packageId} not enabled.`);

                const entryPrice = entryPackage?.price ?? new BigNumber(0);
                const entryCount = entryCountOverride ?? entryPackage?.entryCount ?? 1;
                let commission = new BigNumber(0);
                let commissionRate = 0;

                if (sweepstake.type === SweepstakeType.Platform) {
                    if (isGreaterThanMaxEntry(sweepstake, entryCount))
                        throw new ForbiddenError(`Sweepstake ${sweepstake.id} has reached max capacity.`);

                    if (isGreaterThanMaxEntryPerUser(sweepstake, _.sumBy(entrySets, e => e.count), entryCount))
                        throw new ForbiddenError(`User has maximum entries for sweepstake ${sweepstake.id}.`);

                    if (isGreaterOrEqualToMaxPackagePerUser(entryPackage, entrySets))
                        throw new ForbiddenError(`User has purchased maximum allowance of sweepstake entry package ${packageId}.`);
                }

                let sourceCurrency = currencyCode;
                if (sourceCurrency === Config.baseCurrency)
                    sourceCurrency = Config.defaultCurrency;

                let transfer: TransferResult | undefined;
                let userWalletAccountResolved: UserWalletAccountResolverResult | undefined;
                let sourceCost: BigNumber | undefined;
                let sourceRate: BigNumber | undefined;

                if (entryPrice.isGreaterThan(0) && !skip?.payment) {
                    if (sweepstake.entryPriceType !== SweepstakeEntryPriceType.Cash)
                        throw new Error('Unsupported sweepstake entry price type.');

                    userWalletAccountResolved = await this.userWalletAccountResolver.resolve(sourceCurrency, userId, fundsContext);
                    const sourceAccounts: UserWalletAccounts[] = [];

                    if (!sweepstake.metadata?.excludePlayableFunds)
                        sourceAccounts.push(UserWalletAccounts.Playable);

                    sourceAccounts.push(...[
                        userWalletAccountResolved.context === FundsContext.Custodial ? UserWalletAccounts.DepositCustodial : UserWalletAccounts.Deposit,
                        userWalletAccountResolved.account
                    ]);

                    if (isBaseCurrency(sweepstake) && !currencyMatch(sweepstake, sourceCurrency)) {
                        sourceRate = await this.currencyRateCache.get(Config.baseCurrency, sourceCurrency);
                        sourceCost = entryPrice.times(sourceRate).decimalPlaces(8);
                    }

                    transfer = await this.ledger
                        .transfer(sourceCost ?? entryPrice, sourceCurrency)
                        .purpose(TransactionPurpose.BuyIn)
                        .requestedBy(RequesterType.User, userId)
                        .memo(`Entry set for sweepstake ${sweepstake.id}`)
                        .fromUser(userId, ...sourceAccounts)
                        .toPlatform(PlatformWallets.Corporate)
                        .commit();

                    commissionRate = sweepstake.commissionRate;
                    let contributingAmount = entryPrice;

                    if (sweepstake.metadata?.excludePlayableContributions) {
                        let effectiveAmount = getEffectiveAmount(transfer);

                        if (isBaseCurrency(sweepstake) && !currencyMatch(sweepstake, sourceCurrency) && sourceRate)
                            effectiveAmount = effectiveAmount.dividedBy(sourceRate).decimalPlaces(8);

                        contributingAmount = effectiveAmount;
                    }

                    if (commissionRate > 0)
                        commission = contributingAmount.abs().multipliedBy(commissionRate / 100).decimalPlaces(5);
                }

                const entrySet: NewSweepstakeEntrySet = {
                    state: state ?? SweepstakeEntryState.Confirmed,
                    sweepstakeId: sweepstake.id,
                    userId,
                    count: entryCount,
                    cost: entryPrice,
                    commission,
                    commissionRate,
                    createTime,
                    sourceCost,
                    sourceRate,
                    sourceCurrency,
                    sourceBrand: brand,
                    source
                };

                if (externalId)
                    entrySet.externalId = externalId;

                if (packageId)
                    entrySet.packageId = packageId;

                if (transfer)
                    entrySet.walletEntryId = transfer.entry.id;

                if (userWalletAccountResolved)
                    entrySet.fundsContext = userWalletAccountResolved.context;

                const added = await this.entrySetRepository.add(entrySet);

                await this.participantManager.addOrUpdate(sweepstake, userId);

                if (sweepstake.state <= SweepstakeState.Drawing) {
                    const expireAt = await this.cacheExpiryMapper.from(sweepstake);
                    await this.entrySetCache.upsert(added, expireAt);
                }

                if (!skip?.event)
                    await this.eventDispatcher.send(new SweepstakeEntrySetAddedEvent({
                        sweepstakeId: sweepstake.id,
                        sweepstakeType: sweepstake.type,
                        sweepstakePresentationType: sweepstake.presentationType,
                        sweepstakeMechanism: sweepstake.mechanism,
                        sweepstakeCurrencyCode: sweepstake.currencyCode,
                        userId,
                        entrySetId: added.id,
                        createTime: added.createTime,
                        entryCount: added.count,
                        cost: added.cost,
                        externalId: added.externalId,
                        fundsContext: userWalletAccountResolved?.context,
                        currencyCode: sourceCurrency,
                        sourceBrand: added.sourceBrand,
                        sourceCost: added.sourceCost,
                        sourceRate: added.sourceRate
                    }));

                if (!skip?.websocket)
                    await this.websocket
                        .user(userId)
                        .type('Sweepstake:Entry:Added')
                        .payload({
                            sweepstakeId: sweepstake.id,
                            sweepstakeName: sweepstake.name,
                            sweepstakeNetwork: sweepstake.type === SweepstakeType.Blockchain ? sweepstake.network : undefined,
                            // TODO: Change this, doesn't need to be an array
                            entries: [{
                                id: added.id,
                                state: added.state,
                                userId: added.user.id,
                                displayName: added.user.displayName,
                                createTime: added.createTime
                            }]
                        })
                        .send();

                await this.sweepstakeManager.updateTotals(sweepstake);

                return added;
            });
        });
    }

    public async addExternalId(idOrRef: number | SweepstakeEntrySet, externalId: string): Promise<SweepstakeEntrySet> {
        const entrySet = typeof idOrRef === 'number'
            ? await this.getById(idOrRef)
            : idOrRef;

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${idOrRef} not found.`);

        const updated = await this.entrySetRepository.addExternalId(entrySet.id, externalId);
        await this.entrySetCache.update(updated);

        return updated;
    }

    public async setState(idOrRef: number | SweepstakeEntrySet, state: SweepstakeEntryState): Promise<SweepstakeEntrySet> {
        const entrySet = typeof idOrRef === 'number'
            ? await this.getById(idOrRef)
            : idOrRef;

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${idOrRef} not found.`);

        const updated = await this.entrySetRepository.setState(entrySet.id, state);
        await this.entrySetCache.update(updated);
        await this.participantManager.addOrUpdate(updated.sweepstake.id, updated.user.id);
        await this.sweepstakeManager.updateTotals(updated.sweepstake.id);

        return updated;
    }

    public async confirm(idOrRef: number | SweepstakeEntrySet, options?: SweepstakeEntryConfirmSkipOptions): Promise<SweepstakeEntrySet> {
        const entrySet = typeof idOrRef === 'number'
            ? await this.getById(idOrRef)
            : idOrRef;

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${idOrRef} not found.`);

        const sweepstake = await this.sweepstakeManager.get(entrySet.sweepstake.id);

        if (!sweepstake)
            throw new NotFoundError(`Sweepstake ${entrySet.sweepstake.id} not found.`);

        const updated = await this.entrySetRepository.setState(entrySet.id, SweepstakeEntryState.Confirmed);
        await this.entrySetCache.update(updated);
        await this.participantManager.addOrUpdate(updated.sweepstake.id, updated.user.id);
        await this.sweepstakeManager.updateTotals(updated.sweepstake.id);

        if (!options?.event)
            await this.eventDispatcher.send(new SweepstakeEntrySetAddedEvent({
                sweepstakeId: sweepstake.id,
                sweepstakeType: sweepstake.type,
                sweepstakePresentationType: sweepstake.presentationType,
                sweepstakeMechanism: sweepstake.mechanism,
                sweepstakeCurrencyCode: sweepstake.currencyCode,
                userId: updated.user.id,
                entrySetId: updated.id,
                createTime: updated.createTime,
                entryCount: updated.count,
                cost: updated.cost,
                externalId: updated.externalId,
                fundsContext: entrySet.fundsContext,
                currencyCode: entrySet.sourceCurrency || sweepstake.currencyCode,
                sourceCost: entrySet.sourceCost,
                sourceRate: entrySet.sourceRate,
                sourceBrand: updated.sourceBrand
            }));

        if (!options?.websocket)
            await this.websocket
                .user(updated.user.id)
                .type('Sweepstake:Entry:Added')
                .payload({
                    sweepstakeId: sweepstake.id,
                    sweepstakeName: sweepstake.name,
                    sweepstakeNetwork: sweepstake.type === SweepstakeType.Blockchain ? sweepstake.network : undefined,
                    // TODO: Change this, doesn't need to be an array
                    entries: [{
                        id: updated.id,
                        state: updated.state,
                        userId: updated.user.id,
                        displayName: updated.user.displayName,
                        createTime: updated.createTime
                    }]
                })
                .send();

        return updated;
    }

    public async remove(...items: SweepstakeEntrySet[]): Promise<void> {
        for (const item of items) {
            await this.entrySetRepository.remove(item.id);
            await this.entrySetCache.remove(item);

            const sweepstake = await this.sweepstakeManager.get(item.sweepstake.id);

            if (!sweepstake)
                throw new NotFoundError(`Sweepstake ${item.sweepstake.id} not found.`);
        }

        const entryData = _
            .chain(items)
            .map(i => ({ sweepstakeId: i.sweepstake.id, userId: i.user.id }))
            .uniqBy(i => i.sweepstakeId && i.userId)
            .value();

        for (const data of entryData)
            await this.participantManager.addOrUpdate(data.sweepstakeId, data.userId, true);

        const sweepstakeIds = _
            .chain(items)
            .map(i => i.sweepstake.id)
            .uniq()
            .value();

        for (const id of sweepstakeIds)
            await this.sweepstakeManager.updateTotals(id);
    }

    public async removeById(id: number): Promise<void> {
        const entrySet = await this.getById(id);

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${id} not found.`);

        const sweepstake = await this.sweepstakeManager.get(entrySet.sweepstake.id);

        if (!sweepstake)
            throw new NotFoundError(`Sweepstake ${entrySet.sweepstake.id} not found.`);

        await this.entrySetRepository.remove(entrySet.id);
        await this.entrySetCache.remove(entrySet);
        await this.participantManager.addOrUpdate(entrySet.sweepstake.id, entrySet.user.id, true);
        await this.sweepstakeManager.updateTotals(entrySet.sweepstake.id);
    }

    public async updateCount(id: number, count: number): Promise<SweepstakeEntrySet> {
        const entrySet = await this.getById(id);

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${id} not found.`);

        const updated = await this.entrySetRepository.updateCount(entrySet.id, count);
        await this.entrySetCache.update(updated);

        await this.participantManager.addOrUpdate(entrySet.sweepstake.id, entrySet.user.id, true);
        await this.sweepstakeManager.updateTotals(entrySet.sweepstake.id);

        return updated;
    }

    public async void(idOrRef: number | SweepstakeEntrySet): Promise<SweepstakeEntrySet> {
        const entrySet = typeof idOrRef === 'number'
            ? await this.getById(idOrRef)
            : idOrRef;

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${idOrRef} not found.`);

        if (entrySet.voidTime)
            throw new ForbiddenError(`Sweepstake entry set ${entrySet.id} has already been voided.`);

        const sweepstake = await this.sweepstakeManager.get(entrySet.sweepstake.id);

        if (!sweepstake)
            throw new NotFoundError(`Sweepstake ${entrySet.sweepstake.id} not found.`);

        const updated = await this.entrySetRepository.void(entrySet.id);
        await this.entrySetCache.update(updated);
        await this.participantManager.addOrUpdate(updated.sweepstake.id, updated.user.id, true);
        await this.sweepstakeManager.updateTotals(updated.sweepstake.id);

        return updated;
    }

    public async refund(idOrRef: number | SweepstakeEntrySet): Promise<SweepstakeEntrySet> {
        const entrySet = typeof idOrRef === 'number'
            ? await this.getById(idOrRef)
            : idOrRef;

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${idOrRef} not found.`);

        if (entrySet.cost.isEqualTo(0))
            throw new ForbiddenError(`Sweepstake entry set ${entrySet.id} cost cannot be refunded.`);

        if (entrySet.refundTime)
            throw new ForbiddenError(`Sweepstake entry set ${entrySet.id} has already been refunded.`);

        const sweepstake = await this.sweepstakeManager.get(entrySet.sweepstake.id);

        if (!sweepstake)
            throw new NotFoundError(`Sweepstake ${entrySet.sweepstake.id} not found.`);

        if (!entrySet.walletEntryId) {
            Logger.warn(`Sweepstake entry set ${entrySet.id} does not have a wallet entry ID.`);
            const userWalletAccountResolved = await this.userWalletAccountResolver.resolve(entrySet.fundsContext);

            await this.ledger
                .transfer(entrySet.cost, entrySet.currencyCode)
                .purpose(TransactionPurpose.Refund)
                .requestedBy(RequesterType.System, `Sweepstake:${entrySet.sweepstake.id}`)
                .memo(`Refund for sweepstake ${entrySet.sweepstake.id} entry set ${entrySet.id}`)
                .fromPlatform(PlatformWallets.Corporate)
                .toUser(entrySet.user.id, userWalletAccountResolved.account)
                .commit();
        } else
            await this.ledger
                .refund(entrySet.walletEntryId)
                .memo(`Refund for sweepstake ${sweepstake.id} entry ${entrySet.id}`)
                .requestedBy(RequesterType.System, `Sweepstake:${sweepstake.id}`)
                .commit();

        const updated = await this.entrySetRepository.refund(entrySet.id);
        await this.entrySetCache.update(updated);
        await this.participantManager.addOrUpdate(updated.sweepstake.id, updated.user.id, true);
        await this.sweepstakeManager.updateTotals(updated.sweepstake.id);

        return updated;
    }

    public async refunded(id: number, externalId?: string): Promise<SweepstakeEntrySet> {
        const entrySet = await this.getById(id);

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry ${id} not found.`);

        if (entrySet.cost.isEqualTo(0))
            throw new ForbiddenError(`Sweepstake entry ${entrySet.id} cost cannot be refunded.`);

        if (entrySet.refundTime)
            return entrySet;

        const updated = await this.entrySetRepository.refund(entrySet.id, externalId);
        await this.entrySetCache.update(updated);
        await this.participantManager.addOrUpdate(updated.sweepstake.id, updated.user.id, true);
        await this.sweepstakeManager.updateTotals(updated.sweepstake.id);

        return updated;
    }

    public async award(idOrRef: number | SweepstakeEntrySet, prize: SweepstakePrize): Promise<SweepstakeEntrySetWinner> {
        const entrySet = typeof idOrRef === 'number'
            ? await this.getById(idOrRef)
            : idOrRef;

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${idOrRef} not found.`);

        const award = await this.prizeAwardManager.add(entrySet.id, prize.id);
        const updated = await this.entrySetRepository.awarded(entrySet.id, prize.reward);
        await this.entrySetCache.update(updated);
        await this.sweepstakeManager.updateTotals(updated.sweepstake.id);
        await this.participantManager.award(updated.sweepstake.id, updated.user.id, award);

        return updated;
    }

    public async payout(idOrRef: number | SweepstakeEntrySet): Promise<SweepstakeEntrySet | undefined> {
        const entrySet = typeof idOrRef === 'number'
            ? await this.getById(idOrRef)
            : idOrRef;

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${idOrRef} not found.`);

        if (entrySet.payoutTime)
            return;

        if (entrySet.awards.some(e => !e.claimTime))
            return;

        const updated = await this.entrySetRepository.payout(entrySet.id);
        await this.entrySetCache.update(updated);

        return updated;
    }

    public async updateCache(idOrRef: number | SweepstakeEntrySet): Promise<void> {
        const entrySet = typeof idOrRef === 'number'
            ? await this.getById(idOrRef)
            : idOrRef;

        if (!entrySet)
            return;

        await this.entrySetCache.update(entrySet);
    }

    public async refreshCache(idOrRef: number | SweepstakeEntrySet): Promise<SweepstakeEntrySet> {
        const entrySet = typeof idOrRef === 'number'
            ? await this.entrySetRepository.primary(r => r.getById(idOrRef))
            : idOrRef;

        if (!entrySet)
            throw new NotFoundError(`Sweepstake entry set ${idOrRef} not found.`);

        await this.entrySetCache.update(entrySet);
        return entrySet;
    }

    public async refreshCacheBySweepstake(idOrRef: number | Sweepstake): Promise<void> {
        const sweepstake = typeof idOrRef === 'number'
            ? await this.sweepstakeManager.get(idOrRef)
            : idOrRef;

        if (!sweepstake)
            return;

        const entrySets = await this.entrySetRepository.primary(r => r.getActive(sweepstake.id));

        if (entrySets.length === 0)
            return;

        await this.entrySetCache.clear(sweepstake.id);

        const expireAt = await this.cacheExpiryMapper.from(sweepstake);
        const grouped = _.groupBy(entrySets, i => i.user.id);
        const tasks = Object.entries(grouped).map(([userId, entries]) => this.entrySetCache.store(sweepstake.id, Number(userId), entries, expireAt));
        await Promise.all(tasks);
    }

    public async refreshCacheByUser(sweepstakeId: number, userId: number): Promise<void> {
        const sweepstake = await this.sweepstakeManager.get(sweepstakeId);

        if (!sweepstake)
            return;

        const entrySets = await this.entrySetRepository.get(sweepstakeId, userId);

        if (entrySets.length === 0)
            return;

        const expireAt = await this.cacheExpiryMapper.from(sweepstake);
        await this.entrySetCache.store(sweepstakeId, userId, entrySets, expireAt);
    }

    public async clearCache(sweepstakeId: number): Promise<void> {
        await this.entrySetCache.clear(sweepstakeId);
    }
}