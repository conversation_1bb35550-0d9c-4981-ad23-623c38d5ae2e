import { FundsContext } from '../banking';
import { SweepstakeEntryState } from './sweepstake-entry-state';
import { Sweepstake } from './sweepstake';
import { SweepstakeEntrySkipOptions } from './sweepstake-entry-skip-options';

export interface SweepstakeEntryParams {
    sweepstakeRef: number | Sweepstake;
    userId: number;
    packageId?: number;
    externalId?: string;
    skip?: SweepstakeEntrySkipOptions;
    state?: SweepstakeEntryState;
    fundsContext?: FundsContext;
    currencyCode?: string;
    createTime?: Date;
    brand?: string;
    entryCount?: number;
    source?: string;
}