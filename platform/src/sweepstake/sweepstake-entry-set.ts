import { BigNumber } from '../core/big-number';
import { FundsContext } from '../banking';
import { SweepstakePrizeAward } from './prizes/sweepstake-prize-award';
import { SweepstakeEntryState } from './sweepstake-entry-state';
import { SweepstakeType } from './sweepstake-type';
import { BlockchainNetwork } from '../blockchain/blockchain-network';
import { SweepstakeMetadata } from './sweepstake-metadata';

export interface NewSweepstakeEntrySet {
    state: SweepstakeEntryState;
    sweepstakeId: number;
    userId: number;
    count: number;
    cost?: BigNumber;
    commission?: BigNumber;
    commissionRate?: number;
    externalId?: string;
    packageId?: number;
    walletEntryId?: number;
    fundsContext?: FundsContext;
    createTime?: Date;
    sourceCost?: BigNumber;
    sourceRate?: BigNumber;
    sourceCurrency?: string;
    sourceBrand?: string;
    source?: string;
}

export interface SweepstakeEntrySet {
    id: number;
    state: SweepstakeEntryState;
    sweepstake: {
        id: number;
        type: SweepstakeType;
        name: string;
        network?: BlockchainNetwork;
        metadata?: SweepstakeMetadata;
    };
    user: {
        id: number;
        displayName: string;
    };
    count: number;
    cost: BigNumber;
    commission: BigNumber;
    commissionRate: number;
    totalCost: BigNumber;
    currencyCode: string;
    externalId?: string;
    externalRefundId?: string;
    packageId?: number;
    walletEntryId?: number;
    fundsContext: FundsContext;
    awards: SweepstakePrizeAward[];
    currency?: string;
    awardTime?: Date;
    refundTime?: Date;
    voidTime?: Date;
    payoutTime?: Date;
    createTime: Date;
    updateTime: Date;
    sourceCost?: BigNumber;
    sourceRate?: BigNumber;
    sourceCurrency?: string;
    sourceBrand: string;
    source?: string;
}

export interface SweepstakeEntrySetAwarded extends SweepstakeEntrySet {
    state: SweepstakeEntryState.Awarded;
    awardTime: Date;
}

export interface SweepstakeEntrySetWinner extends SweepstakeEntrySet {
    state: SweepstakeEntryState.Awarded | SweepstakeEntryState.Payout;
    awardTime: Date;
}