import { CryptoCurrencyNetworkConfig } from '../currency-config';
import { TransferDirection } from '../transfer-direction';
import { UserType } from '../../user/user-type';
import { FundsContext } from '../funds-context';

export function isContextSupported(config: CryptoCurrencyNetworkConfig, context: FundsContext, type: UserType): boolean {
    const isSupported = (!config.contexts || config.contexts.length === 0) ? true : config.contexts.includes(context);
    return isSupported || type === UserType.Internal;
}

export function isDirectionSupported(config: CryptoCurrencyNetworkConfig, direction: TransferDirection, type: UserType): boolean {
    const isSupported = (!config.directions || config.directions.length === 0) ? true : config.directions.includes(direction);
    return isSupported || type === UserType.Internal;
}

export function isPublicNetwork(config: CryptoCurrencyNetworkConfig, type: UserType): boolean {
    const isPublic = config.public !== undefined ? config.public : true;
    return isPublic || type === UserType.Internal;
}